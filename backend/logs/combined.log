{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 09:21:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 09:21:26"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/2"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/2","service":"mabourse-api","timestamp":"2025-05-16 09:22:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 09:30:29"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 09:30:29"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-05-16 09:30:51"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 09:30:51"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/2"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/2","service":"mabourse-api","timestamp":"2025-05-16 09:31:08"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 10:16:12"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 10:16:12"}
{"category":"cache","data":{"action":"count","model":"Admin"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: Admin:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"action":"count","model":"Admin"},"level":"debug","message":"[CACHE:SET] Cache set for key: Admin:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"action":"count","model":"User"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: User:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"action":"count","model":"User"},"level":"debug","message":"[CACHE:SET] Cache set for key: User:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"action":"count","model":"Scholarship"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: Scholarship:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"action":"count","model":"Scholarship"},"level":"debug","message":"[CACHE:SET] Cache set for key: Scholarship:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-05-16 10:20:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:20:56"}
{"category":"cache","data":{"action":"count","model":"Admin"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: Admin:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:21:03"}
{"category":"cache","data":{"action":"count","model":"User"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: User:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:21:03"}
{"category":"cache","data":{"action":"count","model":"Scholarship"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: Scholarship:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:21:03"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 10:22:35"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 10:22:36"}
{"category":"cache","data":{"action":"count","model":"Admin"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: Admin:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"action":"count","model":"Admin"},"level":"debug","message":"[CACHE:SET] Cache set for key: Admin:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"action":"count","model":"User"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: User:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"action":"count","model":"User"},"level":"debug","message":"[CACHE:SET] Cache set for key: User:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"action":"count","model":"Scholarship"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: Scholarship:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"action":"count","model":"Scholarship"},"level":"debug","message":"[CACHE:SET] Cache set for key: Scholarship:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 10:28:45"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 10:28:45"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-05-16 10:30:06"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:30:06"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-05-16 10:32:41"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:32:41"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-05-16 10:33:57"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:33:57"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 10:33:58"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 10:33:58"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-05-25 18:18:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-25 18:18:42"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-25 18:19:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-25 18:19:00"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-05-26 17:03:09"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-26 17:03:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-26 17:03:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-26 17:03:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-05-26 17:03:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-26 17:03:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-21 23:13:52"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-21 23:13:53"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-21 23:13:57"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-21 23:13:57"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-06-21 23:14:13"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-06-21 23:14:13"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-21 23:25:40"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-21 23:25:40"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 12:28:30"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:28:33"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Master"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Master","service":"mabourse-api","timestamp":"2025-06-22 12:28:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Master"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Master with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:28:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Master&country=Canada"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Master&country=Canada","service":"mabourse-api","timestamp":"2025-06-22 12:28:43"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Master&country=Canada"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Master&country=Canada with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:28:43"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 12:34:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:34:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 12:34:31"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:34:31"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 12:45:26"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:45:26"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 12:45:28"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:45:28"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 12:57:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:57:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 12:59:15"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:59:15"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 14:15:18"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 14:15:18"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 14:16:34"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 14:16:34"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 15:20:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 15:20:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 16:46:02"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 16:46:02"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence","service":"mabourse-api","timestamp":"2025-06-22 16:46:15"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 16:46:15"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence&country=Canada"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence&country=Canada","service":"mabourse-api","timestamp":"2025-06-22 16:46:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence&country=Canada"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence&country=Canada with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 16:46:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 16:48:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 16:48:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 17:19:03"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 17:19:03"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 17:21:58"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 17:21:58"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 17:25:52"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 17:25:52"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 20:41:05"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 20:41:05"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-09 13:08:50"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-09 13:08:50"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-09 13:08:51"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-09 13:08:52"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-09 13:09:22"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-09 13:09:22"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-09 13:09:51"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-09 13:09:51"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-09 13:11:13"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-09 13:11:13"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence","service":"mabourse-api","timestamp":"2025-07-09 13:13:18"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-09 13:13:18"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence&country=France"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence&country=France","service":"mabourse-api","timestamp":"2025-07-09 13:13:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence&country=France"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence&country=France with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-09 13:13:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-09 13:13:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-09 13:13:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-09 13:14:02"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-09 13:14:02"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-09 13:15:06"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-09 13:15:06"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 15:47:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 15:47:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 15:47:57"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 15:47:59"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 15:52:54"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 15:52:54"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 15:53:01"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 15:53:01"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 16:08:26"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 16:08:26"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 19:31:41"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 19:31:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 19:35:16"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 19:35:16"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?page=1&limit=5"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships?page=1&limit=5","service":"mabourse-api","timestamp":"2025-07-11 22:37:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?page=1&limit=5"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships?page=1&limit=5 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 22:37:48"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:39:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:39:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:39:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:39:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:39:48"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:42:28"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 22:42:28"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:42:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 22:42:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:44:35"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:44:37"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 22:44:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:44:46"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:44:46"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:47"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:47"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:47"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:47"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:45:03"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:45:03"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:45:16"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 22:45:16"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:45:18"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:45:20"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:59:59"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 22:59:59"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:07:44"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:07:44"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:11:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:11:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:13:07"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:16:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:16:41"}
{"category":"cache","data":{"count":1,"keys":["GET:/api/scholarships"]},"level":"info","message":"[CACHE:INVALIDATE] Cache invalidated for key: /api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:21:45"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?limit=20"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships?limit=20","service":"mabourse-api","timestamp":"2025-07-11 23:23:12"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?limit=20"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships?limit=20 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:23:12"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?q=STEM"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?q=STEM","service":"mabourse-api","timestamp":"2025-07-11 23:24:11"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?q=STEM"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?q=STEM with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:24:11"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?level=undergraduate"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships?level=undergraduate","service":"mabourse-api","timestamp":"2025-07-11 23:24:30"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?level=undergraduate"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships?level=undergraduate with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:24:30"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:26:39"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 23:26:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:26:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:26:40"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:26:40"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:26:40"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:26:40"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 23:26:50"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:26:50"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 23:26:50"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:26:54"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:26:54"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:26:54"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:26:54"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:28:01"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:28:01"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:28:01"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:28:01"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:30:24"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 23:30:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:30:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:30:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:30:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:30:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 23:30:33"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 23:30:33"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:30:37"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:30:40"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:30:42"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:30:46"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:30:48"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:30:50"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:30:52"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:36:14"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 23:36:14"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:36:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?level=undergraduate&limit=5"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships?level=undergraduate&limit=5","service":"mabourse-api","timestamp":"2025-07-11 23:36:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?level=undergraduate&limit=5"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships?level=undergraduate&limit=5 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:36:24"}
{"category":"cache","data":{"count":1,"keys":["GET:/api/scholarships?level=undergraduate&limit=5"]},"level":"info","message":"[CACHE:INVALIDATE] Cache invalidated for key: /api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:40:06"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?limit=3"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships?limit=3","service":"mabourse-api","timestamp":"2025-07-11 23:47:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?limit=3"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships?limit=3 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:47:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:47:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:47:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:47:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:47:53"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:47:53"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 23:48:18"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:48:18"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:48:59"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 23:48:59"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:48:59"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:48:59"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 23:49:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 23:49:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:00"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:49:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 23:49:33"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 23:49:33"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:49:39"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:49:46"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 23:49:47"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:08:13"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:08:13"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 00:08:20"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 00:08:20"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 00:08:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-12 00:08:30"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 00:08:30"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:08:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:09:02"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:09:31"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:09:31"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:10:37"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:10:37"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:15:20"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:15:20"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 00:15:20"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 00:15:21"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:16:59"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:16:59"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:21:51"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:21:51"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 00:21:59"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 00:21:59"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 00:22:08"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-12 00:22:15"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 00:22:15"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:23:20"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:23:20"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?page=1&limit=10"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships?page=1&limit=10","service":"mabourse-api","timestamp":"2025-07-12 00:23:26"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?page=1&limit=10"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships?page=1&limit=10 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 00:23:26"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:33:33"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:33:33"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:35:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:35:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:38:15"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:38:15"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 00:39:51"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 00:39:51"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 01:13:19"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 01:13:19"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 01:16:19"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 01:16:19"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 01:20:36"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 01:20:36"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 01:24:13"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 01:24:13"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 01:24:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 01:24:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 01:24:25"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 01:27:33"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 01:27:33"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 01:29:08"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 01:29:08"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 01:35:57"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 01:35:57"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 01:36:47"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 08:34:36"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 08:34:36"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 08:48:24"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 08:48:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 08:48:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 08:48:25"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 08:56:11"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 08:56:11"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 08:56:11"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 08:56:11"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 08:56:17"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 09:02:44"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 09:02:44"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 09:05:02"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 09:05:02"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 09:10:12"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 09:10:12"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 09:11:45"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 09:11:45"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 10:00:43"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 10:00:43"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 10:01:38"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 10:06:46"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 10:06:46"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 11:48:47"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 11:48:47"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 12:45:38"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 12:45:38"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 12:45:38"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 12:45:38"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 12:45:38"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 12:45:38"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 12:45:38"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 12:57:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 12:57:25"}
{"error":{"message":"Not Found - /api/admin/stats","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /api/admin/stats","request":{"method":"GET","url":"/api/admin/stats"},"service":"mabourse-api","timestamp":"2025-07-12 14:37:50"}
{"error":{"message":"Not Found - /api/newsletter/subscribers","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /api/newsletter/subscribers","request":{"method":"GET","url":"/api/newsletter/subscribers"},"service":"mabourse-api","timestamp":"2025-07-12 14:38:00"}
{"error":{"message":"Not Found - /api/admin/analytics","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /api/admin/analytics","request":{"method":"GET","url":"/api/admin/analytics"},"service":"mabourse-api","timestamp":"2025-07-12 14:38:05"}
{"error":{"message":"Not Found - /api/admin/security/events","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /api/admin/security/events","request":{"method":"GET","url":"/api/admin/security/events"},"service":"mabourse-api","timestamp":"2025-07-12 14:38:29"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 14:49:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 14:49:18"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 14:49:49"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 14:49:49"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 14:50:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 16:13:43"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 16:13:44"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-12 16:14:04"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 16:14:04"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 16:22:12"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 16:22:12"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-12 16:22:45"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 16:22:45"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/8"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/8","service":"mabourse-api","timestamp":"2025-07-12 16:22:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/8"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/8 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 16:22:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 17:01:29"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 17:01:29"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 17:01:47"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 17:01:47"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 17:19:28"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 17:19:28"}
{"error":{"message":"Not Found - /api/auth/login","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /api/auth/login","request":{"method":"POST","url":"/api/auth/login"},"service":"mabourse-api","timestamp":"2025-07-12 17:23:49"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 17:30:30"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 17:30:30"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 17:32:40"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 17:32:46"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 17:39:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 17:39:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-12 17:40:04"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-12 17:40:17"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-12 17:40:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/5"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/5","service":"mabourse-api","timestamp":"2025-07-12 17:40:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/5"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/5 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-12 17:40:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 01:34:34"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 01:34:34"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 01:35:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-13 01:35:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 01:35:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 01:41:28"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 01:41:28"}
{"error":{"message":"Not Found - /api/newsletter/subscriptions","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /api/newsletter/subscriptions","request":{"method":"GET","url":"/api/newsletter/subscriptions"},"service":"mabourse-api","timestamp":"2025-07-13 01:41:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 01:41:54"}
{"error":{"message":"Not Found - /health","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /health","request":{"method":"GET","url":"/health"},"service":"mabourse-api","timestamp":"2025-07-13 01:42:27"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-13 01:42:50"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-13 01:42:50"}
{"error":{"message":"Not Found - /api/newsletter/subscribe","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /api/newsletter/subscribe","request":{"method":"POST","url":"/api/newsletter/subscribe"},"service":"mabourse-api","timestamp":"2025-07-13 01:43:17"}
{"error":{"message":"Not Found - /api/admin/admins","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /api/admin/admins","request":{"method":"GET","url":"/api/admin/admins"},"service":"mabourse-api","timestamp":"2025-07-13 01:46:59"}
{"error":{"message":"Not Found - /api/2fa/setup","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /api/2fa/setup","request":{"method":"GET","url":"/api/2fa/setup"},"service":"mabourse-api","timestamp":"2025-07-13 01:53:59"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?q=excellence"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?q=excellence","service":"mabourse-api","timestamp":"2025-07-13 01:54:59"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?q=excellence"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?q=excellence with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 01:54:59"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?level=undergraduate"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships?level=undergraduate","service":"mabourse-api","timestamp":"2025-07-13 01:55:20"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?level=undergraduate"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships?level=undergraduate with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 01:55:20"}
{"category":"cache","data":{"count":2,"keys":["GET:/api/scholarships/search?q=excellence","GET:/api/scholarships?level=undergraduate"]},"level":"info","message":"[CACHE:INVALIDATE] Cache invalidated for key: /api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 01:56:10"}
{"error":{"message":"Not Found - /api/newsletter","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /api/newsletter","request":{"method":"DELETE","url":"/api/newsletter"},"service":"mabourse-api","timestamp":"2025-07-13 01:59:29"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 02:10:45"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 02:10:45"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 02:10:52"}
{"category":"cache","data":{"count":1,"keys":["GET:/api/scholarships"]},"level":"info","message":"[CACHE:INVALIDATE] Cache invalidated for key: /api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 02:14:27"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/15"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/15","service":"mabourse-api","timestamp":"2025-07-13 02:18:55"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/15"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/15 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 02:18:55"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 02:28:40"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 02:28:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 03:17:58"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:17:58"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-13 03:20:35"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:20:35"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 03:36:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:36:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-13 03:36:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:36:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 03:41:59"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:41:59"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-13 03:42:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:42:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?limit=3"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships?limit=3","service":"mabourse-api","timestamp":"2025-07-13 03:49:22"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?limit=3"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships?limit=3 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:49:22"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?limit=2"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships?limit=2","service":"mabourse-api","timestamp":"2025-07-13 03:49:29"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?limit=2"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships?limit=2 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:49:29"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 03:51:01"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:51:02"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-13 03:51:11"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:51:11"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/11"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/11","service":"mabourse-api","timestamp":"2025-07-13 03:51:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/11"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/11 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:51:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 03:57:28"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 03:57:28"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-13 04:02:04"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 04:02:04"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:04:59"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:04:59"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:04:59"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:04:59"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:04:59"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:04:59"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:04:59"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:01"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:01"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:01"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:01"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:01"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:01"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:01"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:04"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:04"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:04"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:04"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:04"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:04"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:04"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155359933-c1e348a37a53c94149fe8c8e7ac3da0b_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:08"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747153473628-363f53c6bc7544b51f1c757c18dabe28_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:08"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362050-e4b07b2113652093018062710c61e0fb_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:08"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155362420-4f81b43e34cc293111e517ad6af4c508_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:08"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/DAAD_Scholarship_174-1747155361390-3bb07a2e9445717f73052cf281a778d5_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:08"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/Chevening_Scholarshi-1747155900356-56a34130e3b46607e3c7daa331943ccd_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:08"}
{"error":{"message":"Not Found - /uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp","name":"AppError"},"level":"warn","message":"[NOT_FOUND_ERROR] Not Found - /uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp","request":{"method":"GET","url":"/uploads/scholarships/thumbnails/Bourse_du_gouvernmen-1747156017163-374244a1d4040ab6882965aaa076f59f_card.webp"},"service":"mabourse-api","timestamp":"2025-07-13 04:05:08"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-13 04:13:31"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-13 04:13:31"}
