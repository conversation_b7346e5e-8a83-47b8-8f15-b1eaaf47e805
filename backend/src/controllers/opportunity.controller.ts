/**
 * Opportunity Controller
 * 
 * Handles all opportunity-related operations with industry-standard practices
 */

import { Request, Response } from 'express';
import { Opportunity, OpportunityData } from '../models/Opportunity';
import { sendSuccess, sendError } from '../utils/response.util';
import { query } from '../config/database';
import { validateOpportunityData } from '../utils/validation.util';

/**
 * Get all opportunities with filtering and pagination
 */
export const getAllOpportunities = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 10,
      type,
      location,
      isRemote,
      active,
      orderBy = 'created_at',
      orderDirection = 'DESC'
    } = req.query;

    const offset = (Number(page) - 1) * Number(limit);
    
    const options = {
      limit: Number(limit),
      offset,
      type: type as string,
      location: location as string,
      isRemote: isRemote === 'true' ? true : isRemote === 'false' ? false : undefined,
      isActive: active === 'true' ? true : active === 'false' ? false : undefined,
      orderBy: orderBy as 'created_at' | 'deadline' | 'title' | 'start_date',
      orderDirection: orderDirection as 'ASC' | 'DESC'
    };

    const opportunities = await Opportunity.findAll(options);
    
    sendSuccess(res, 'Opportunities retrieved successfully', {
      opportunities,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: opportunities.length
      }
    });
  } catch (error) {
    console.error('Error fetching opportunities:', error);
    sendError(res, 'Failed to fetch opportunities', error);
  }
};

/**
 * Get active opportunities (not expired)
 */
export const getActiveOpportunities = async (req: Request, res: Response): Promise<void> => {
  try {
    const { limit } = req.query;
    
    const opportunities = await Opportunity.findActive(limit ? Number(limit) : undefined);
    
    sendSuccess(res, 'Active opportunities retrieved successfully', opportunities);
  } catch (error) {
    console.error('Error fetching active opportunities:', error);
    sendError(res, 'Failed to fetch active opportunities', error);
  }
};

/**
 * Get opportunity by ID
 */
export const getOpportunityById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id || isNaN(Number(id))) {
      sendError(res, 'Invalid opportunity ID', null, 400);
      return;
    }

    const opportunity = await Opportunity.findById(Number(id));
    
    if (!opportunity) {
      sendError(res, 'Opportunity not found', null, 404);
      return;
    }

    sendSuccess(res, 'Opportunity retrieved successfully', opportunity);
  } catch (error) {
    console.error('Error fetching opportunity:', error);
    sendError(res, 'Failed to fetch opportunity', error);
  }
};

/**
 * Get opportunities by type
 */
export const getOpportunitiesByType = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type } = req.params;
    const { limit } = req.query;
    
    if (!type) {
      sendError(res, 'Type is required', null, 400);
      return;
    }

    const validTypes = ['internship', 'training', 'conference', 'workshop', 'competition'];
    if (!validTypes.includes(type)) {
      sendError(res, 'Invalid opportunity type', null, 400);
      return;
    }

    const opportunities = await Opportunity.findByType(type, limit ? Number(limit) : undefined);
    
    sendSuccess(res, 'Opportunities retrieved successfully', opportunities);
  } catch (error) {
    console.error('Error fetching opportunities by type:', error);
    sendError(res, 'Failed to fetch opportunities', error);
  }
};

/**
 * Search opportunities
 */
export const searchOpportunities = async (req: Request, res: Response): Promise<void> => {
  try {
    const { q, type, location, isRemote, page = 1, limit = 10 } = req.query;
    
    if (!q || typeof q !== 'string') {
      sendError(res, 'Search query is required', null, 400);
      return;
    }

    const offset = (Number(page) - 1) * Number(limit);
    
    const options = {
      type: type as string,
      location: location as string,
      isRemote: isRemote === 'true' ? true : isRemote === 'false' ? false : undefined,
      limit: Number(limit),
      offset
    };

    const opportunities = await Opportunity.search(q, options);
    
    sendSuccess(res, 'Search completed successfully', {
      opportunities,
      query: q,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: opportunities.length
      }
    });
  } catch (error) {
    console.error('Error searching opportunities:', error);
    sendError(res, 'Failed to search opportunities', error);
  }
};

/**
 * Create new opportunity (Admin only)
 */
export const createOpportunity = async (req: Request, res: Response): Promise<void> => {
  try {
    const opportunityData: Omit<OpportunityData, 'id' | 'createdAt' | 'updatedAt'> = req.body;
    
    // Validate opportunity data
    const validation = validateOpportunityData(opportunityData);
    if (!validation.isValid) {
      sendError(res, 'Validation failed', validation.errors, 400);
      return;
    }

    // Add admin info
    opportunityData.createdByAdmin = (req as any).admin?.id || null;

    // Ensure deadline is a valid Date
    if (opportunityData.deadline && typeof opportunityData.deadline === 'string') {
      try {
        const date = new Date(opportunityData.deadline);
        if (!isNaN(date.getTime())) {
          opportunityData.deadline = date;
        }
      } catch (err) {
        sendError(res, 'Invalid deadline date format', null, 400);
        return;
      }
    }

    const opportunity = await Opportunity.create(opportunityData);
    
    sendSuccess(res, 'Opportunity created successfully', opportunity, 201);
  } catch (error) {
    console.error('Error creating opportunity:', error);
    sendError(res, 'Failed to create opportunity', error);
  }
};

/**
 * Update opportunity (Admin only)
 */
export const updateOpportunity = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData: Partial<OpportunityData> = req.body;
    
    if (!id || isNaN(Number(id))) {
      sendError(res, 'Invalid opportunity ID', null, 400);
      return;
    }

    // Check if opportunity exists
    const existingOpportunity = await Opportunity.findById(Number(id));
    if (!existingOpportunity) {
      sendError(res, 'Opportunity not found', null, 404);
      return;
    }

    // Validate update data
    const validation = validateOpportunityData(updateData, true);
    if (!validation.isValid) {
      sendError(res, 'Validation failed', validation.errors, 400);
      return;
    }

    // Ensure deadline is a valid Date if provided
    if (updateData.deadline && typeof updateData.deadline === 'string') {
      try {
        const date = new Date(updateData.deadline);
        if (!isNaN(date.getTime())) {
          updateData.deadline = date;
        } else {
          sendError(res, 'Invalid deadline date format', null, 400);
          return;
        }
      } catch (err) {
        sendError(res, 'Invalid deadline date format', null, 400);
        return;
      }
    }

    const opportunity = await Opportunity.update(Number(id), updateData);
    
    if (!opportunity) {
      sendError(res, 'Failed to update opportunity', null, 500);
      return;
    }

    sendSuccess(res, 'Opportunity updated successfully', opportunity);
  } catch (error) {
    console.error('Error updating opportunity:', error);
    sendError(res, 'Failed to update opportunity', error);
  }
};

/**
 * Delete opportunity (Admin only)
 */
export const deleteOpportunity = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id || isNaN(Number(id))) {
      sendError(res, 'Invalid opportunity ID', null, 400);
      return;
    }

    // Check if opportunity exists
    const existingOpportunity = await Opportunity.findById(Number(id));
    if (!existingOpportunity) {
      sendError(res, 'Opportunity not found', null, 404);
      return;
    }

    const deleted = await Opportunity.delete(Number(id));
    
    if (!deleted) {
      sendError(res, 'Failed to delete opportunity', null, 500);
      return;
    }

    sendSuccess(res, 'Opportunity deleted successfully', null);
  } catch (error) {
    console.error('Error deleting opportunity:', error);
    sendError(res, 'Failed to delete opportunity', error);
  }
};

/**
 * Get opportunity types for dropdown menu
 */
export const getOpportunityTypes = async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await query(`
      SELECT
        type,
        COUNT(*) as count,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_count
      FROM opportunities
      WHERE type IS NOT NULL AND type != ''
      GROUP BY type
      ORDER BY count DESC
    `);

    const types = result.rows.map(row => ({
      name: row.type,
      count: parseInt(row.count),
      activeCount: parseInt(row.active_count),
      slug: row.type.toLowerCase().replace(/\s+/g, '-')
    }));

    sendSuccess(res, 'Opportunity types retrieved successfully', types);
  } catch (error) {
    console.error('Error fetching opportunity types:', error);
    sendError(res, 'Failed to fetch opportunity types', error);
  }
};
