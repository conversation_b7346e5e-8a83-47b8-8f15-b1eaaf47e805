/**
 * Enhanced Header Component
 * 
 * Professional navigation header with dropdown menus,
 * mobile responsiveness, and industry-standard interactions.
 */

import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';
import LanguageSwitcher from '../common/LanguageSwitcher';
import NavigationDropdown from '../navigation/NavigationDropdown';
import MobileNavigationDropdown from '../navigation/MobileNavigationDropdown';

const EnhancedHeader: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();
  const { translations } = useLanguage();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  const isActive = (path: string) => location.pathname === path;

  const navigationItems = [
    {
      path: '/',
      label: translations.navigation.home,
      exact: true
    },
    {
      path: '/about',
      label: translations.navigation.about
    },
    {
      path: '/guides',
      label: translations.navigation.guides
    },
    {
      path: '/contact',
      label: translations.navigation.contact
    }
  ];

  return (
    <header className={`
      fixed w-full top-0 z-50 transition-all duration-300 ease-in-out
      ${isScrolled 
        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100' 
        : 'bg-white shadow-sm'
      }
    `}>
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-3 group">
              <div className="relative">
                <img
                  src="/assets/images/MaBoursedetudeLogo.jpeg"
                  alt={translations.brand.name}
                  className="h-12 w-auto rounded-lg shadow-md transform transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <div className="flex flex-col">
                <span className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight">
                  {translations.brand.name}
                </span>
                <span className="text-xs text-gray-500 font-medium tracking-wider">
                  {translations.brand.tagline}
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {/* Home Link */}
            <Link
              to="/"
              className={`
                px-3 py-2 rounded-md text-sm font-medium
                transition-all duration-200 ease-in-out
                ${isActive('/')
                  ? 'text-primary bg-primary/10'
                  : 'text-gray-700 hover:text-primary hover:bg-primary/5'
                }
              `}
            >
              <span className="transition-colors duration-200">{translations.navigation.home}</span>
            </Link>

            {/* Dropdown Menus */}
            <NavigationDropdown
              type="countries"
              label={translations.navigation.countries}
            />
            
            <NavigationDropdown
              type="scholarships"
              label={translations.navigation.scholarships}
            />
            
            <NavigationDropdown
              type="opportunities"
              label={translations.navigation.opportunities}
            />

            {/* Regular Navigation Items */}
            {navigationItems.slice(1).map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`
                  px-3 py-2 rounded-md text-sm font-medium
                  transition-all duration-200 ease-in-out
                  ${isActive(item.path)
                    ? 'text-primary bg-primary/10'
                    : 'text-gray-700 hover:text-primary hover:bg-primary/5'
                  }
                `}
              >
                <span className="transition-colors duration-200">{item.label}</span>
              </Link>
            ))}
          </div>

          {/* Right Side - Language Switcher & Mobile Menu */}
          <div className="flex items-center space-x-4">
            <LanguageSwitcher />
            
            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary transition-colors duration-200"
                aria-expanded={isMobileMenuOpen}
                aria-label="Toggle navigation menu"
              >
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="bg-white border-t border-gray-200 shadow-lg max-h-screen overflow-y-auto">
            {/* Mobile Navigation Items */}
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`
                    block px-3 py-3 rounded-md text-base font-medium
                    transition-colors duration-200 ease-in-out
                    ${isActive(item.path)
                      ? 'text-primary bg-primary/10 border-l-4 border-primary'
                      : 'text-gray-700 hover:text-primary hover:bg-gray-50'
                    }
                  `}
                >
                  <span>{item.label}</span>
                </Link>
              ))}
            </div>

            {/* Mobile Dropdown Sections */}
            <div className="border-t border-gray-200">
              <div className="px-4 py-3 text-xs font-semibold text-gray-500 uppercase tracking-wider bg-gray-50">
                Browse Categories
              </div>

              <MobileNavigationDropdown
                type="countries"
                label={translations.navigation.countries}
                onItemClick={() => setIsMobileMenuOpen(false)}
              />

              <MobileNavigationDropdown
                type="scholarships"
                label={translations.navigation.scholarships}
                onItemClick={() => setIsMobileMenuOpen(false)}
              />

              <MobileNavigationDropdown
                type="opportunities"
                label={translations.navigation.opportunities}
                onItemClick={() => setIsMobileMenuOpen(false)}
              />
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default EnhancedHeader;
