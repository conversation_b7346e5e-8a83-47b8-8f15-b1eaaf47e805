/**
 * Professional Dropdown Component
 * 
 * Industry-standard dropdown with proper hover/click interactions,
 * accessibility features, and smooth animations.
 */

import React, { useState, useRef, useEffect, ReactNode } from 'react';
import { ChevronDown } from 'lucide-react';

export interface DropdownItem {
  id: string;
  label: string;
  href?: string;
  onClick?: () => void;
  count?: number;
  icon?: ReactNode;
  disabled?: boolean;
}

export interface DropdownProps {
  trigger: ReactNode;
  items: DropdownItem[];
  className?: string;
  dropdownClassName?: string;
  placement?: 'bottom-left' | 'bottom-right' | 'bottom-center';
  showOnHover?: boolean;
  closeOnClick?: boolean;
  maxHeight?: string;
  loading?: boolean;
  emptyMessage?: string;
  onOpen?: () => void;
  onClose?: () => void;
}

const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  items,
  className = '',
  dropdownClassName = '',
  placement = 'bottom-left',
  showOnHover = true,
  closeOnClick = true,
  maxHeight = '300px',
  loading = false,
  emptyMessage = 'No items available',
  onOpen,
  onClose
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check if mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        closeDropdown();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        closeDropdown();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen]);

  const openDropdown = () => {
    if (!isOpen) {
      setIsOpen(true);
      onOpen?.();
    }
  };

  const closeDropdown = () => {
    if (isOpen) {
      setIsOpen(false);
      onClose?.();
    }
  };

  const handleMouseEnter = () => {
    if (showOnHover && !isMobile) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      // Slight delay for smoother interaction
      timeoutRef.current = setTimeout(() => {
        openDropdown();
      }, 100);
    }
  };

  const handleMouseLeave = () => {
    if (showOnHover && !isMobile) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        closeDropdown();
      }, 300); // Longer delay to prevent accidental closure
    }
  };

  const handleClick = () => {
    if (isMobile || !showOnHover) {
      if (isOpen) {
        closeDropdown();
      } else {
        openDropdown();
      }
    }
  };

  const handleItemClick = (item: DropdownItem) => {
    if (item.disabled) return;
    
    if (item.onClick) {
      item.onClick();
    }
    
    if (item.href) {
      window.location.href = item.href;
    }
    
    if (closeOnClick) {
      closeDropdown();
    }
  };

  const getPlacementClasses = () => {
    switch (placement) {
      case 'bottom-right':
        return 'right-0';
      case 'bottom-center':
        return 'left-1/2 transform -translate-x-1/2';
      default:
        return 'left-0';
    }
  };

  return (
    <div
      ref={dropdownRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Trigger */}
      <div
        onClick={handleClick}
        className="cursor-pointer select-none"
        role="button"
        tabIndex={0}
        aria-expanded={isOpen}
        aria-haspopup="true"
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleClick();
          }
        }}
      >
        {trigger}
      </div>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className={`
            absolute z-50 mt-2 min-w-[200px] bg-white rounded-lg shadow-xl border border-gray-200
            transform transition-all duration-300 ease-out
            animate-in slide-in-from-top-2 fade-in-0
            ${isOpen ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-95 -translate-y-2'}
            ${getPlacementClasses()}
            ${dropdownClassName}
          `}
          style={{
            maxHeight,
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
          }}
        >
          {loading ? (
            <div className="px-4 py-3 text-center text-gray-500">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary mx-auto"></div>
              <span className="text-sm mt-2 block">Loading...</span>
            </div>
          ) : items.length === 0 ? (
            <div className="px-4 py-3 text-center text-gray-500 text-sm">
              {emptyMessage}
            </div>
          ) : (
            <div className="py-1 max-h-80 overflow-y-auto">
              {items.map((item) => (
                <div
                  key={item.id}
                  onClick={() => handleItemClick(item)}
                  className={`
                    px-4 py-2 text-sm cursor-pointer flex items-center justify-between
                    transition-colors duration-150 ease-in-out
                    ${item.disabled 
                      ? 'text-gray-400 cursor-not-allowed' 
                      : 'text-gray-700 hover:bg-gray-50 hover:text-primary'
                    }
                  `}
                  role="menuitem"
                  tabIndex={item.disabled ? -1 : 0}
                  onKeyDown={(e) => {
                    if ((e.key === 'Enter' || e.key === ' ') && !item.disabled) {
                      e.preventDefault();
                      handleItemClick(item);
                    }
                  }}
                >
                  <div className="flex items-center space-x-2">
                    {item.icon && (
                      <span className="text-gray-400">{item.icon}</span>
                    )}
                    <span>{item.label}</span>
                  </div>
                  {item.count !== undefined && (
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                      {item.count}
                    </span>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Dropdown;
