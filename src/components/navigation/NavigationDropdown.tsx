/**
 * Navigation Dropdown Component
 * 
 * Specialized dropdown for navigation menus with data fetching,
 * proper routing, and professional styling.
 */

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown, MapPin, GraduationCap, Briefcase } from 'lucide-react';
import Dropdown, { DropdownItem } from '../common/Dropdown';
import { useLanguage } from '../../context/LanguageContext';

interface NavigationDropdownProps {
  type: 'countries' | 'scholarships' | 'opportunities';
  label: string;
  className?: string;
}

interface CountryData {
  name: string;
  count: number;
  slug: string;
}

interface LevelData {
  name: string;
  count: number;
  openCount: number;
  slug: string;
}

interface OpportunityTypeData {
  name: string;
  count: number;
  activeCount: number;
  slug: string;
}

const NavigationDropdown: React.FC<NavigationDropdownProps> = ({
  type,
  label,
  className = ''
}) => {
  const [items, setItems] = useState<DropdownItem[]>([]);
  const [loading, setLoading] = useState(false);
  const { translations } = useLanguage();

  // Fetch data when dropdown opens
  const fetchData = async () => {
    if (items.length > 0) return; // Don't refetch if we already have data
    
    setLoading(true);
    try {
      let endpoint = '';
      let dataProcessor: (data: any[]) => DropdownItem[] = () => [];

      switch (type) {
        case 'countries':
          endpoint = '/api/countries';
          dataProcessor = (countries: CountryData[]) => [
            {
              id: 'all-countries',
              label: translations.navigation.allCountries || 'All Countries',
              href: '/countries',
              icon: <MapPin size={16} />,
              count: countries.reduce((sum, c) => sum + c.count, 0)
            },
            ...countries.slice(0, 8).map(country => ({
              id: country.slug,
              label: country.name,
              href: `/countries/${encodeURIComponent(country.name)}`,
              count: country.count
            })),
            ...(countries.length > 8 ? [{
              id: 'view-all-countries',
              label: translations.navigation.viewAll || 'View All',
              href: '/countries'
            }] : [])
          ];
          break;

        case 'scholarships':
          endpoint = '/api/scholarships/levels';
          dataProcessor = (levels: LevelData[]) => [
            {
              id: 'all-scholarships',
              label: translations.navigation.allScholarships || 'All Scholarships',
              href: '/scholarships',
              icon: <GraduationCap size={16} />,
              count: levels.reduce((sum, l) => sum + l.count, 0)
            },
            ...levels.map(level => ({
              id: level.slug,
              label: level.name,
              href: `/scholarships?level=${encodeURIComponent(level.name)}`,
              count: level.openCount
            }))
          ];
          break;

        case 'opportunities':
          endpoint = '/api/opportunities/types';
          dataProcessor = (types: OpportunityTypeData[]) => [
            {
              id: 'all-opportunities',
              label: translations.navigation.allOpportunities || 'All Opportunities',
              href: '/opportunities',
              icon: <Briefcase size={16} />,
              count: types.reduce((sum, t) => sum + t.count, 0)
            },
            ...types.map(opType => ({
              id: opType.slug,
              label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),
              href: `/opportunities?type=${encodeURIComponent(opType.name)}`,
              count: opType.activeCount
            }))
          ];
          break;
      }

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);
      if (!response.ok) throw new Error('Failed to fetch data');
      
      const result = await response.json();
      const data = result.data || result;
      
      setItems(dataProcessor(data));
    } catch (error) {
      console.error(`Error fetching ${type} data:`, error);
      setItems([{
        id: 'error',
        label: 'Failed to load data',
        disabled: true
      }]);
    } finally {
      setLoading(false);
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'countries':
        return <MapPin size={16} className="text-gray-500" />;
      case 'scholarships':
        return <GraduationCap size={16} className="text-gray-500" />;
      case 'opportunities':
        return <Briefcase size={16} className="text-gray-500" />;
      default:
        return null;
    }
  };

  const trigger = (
    <div className={`
      flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium
      transition-all duration-300 ease-in-out
      text-gray-700 hover:text-primary hover:bg-primary/5 hover:shadow-sm
      group-hover:scale-105 transform
      ${className}
    `}>
      {getIcon()}
      <span className="transition-all duration-200">{label}</span>
      <ChevronDown
        size={16}
        className="text-gray-400 transition-all duration-300 ease-out group-hover:rotate-180 group-hover:text-primary"
      />
    </div>
  );

  return (
    <Dropdown
      trigger={trigger}
      items={items}
      loading={loading}
      onOpen={fetchData}
      showOnHover={true}
      closeOnClick={true}
      placement="bottom-left"
      className="group"
      dropdownClassName="border-t-2 border-primary"
      emptyMessage={`No ${type} available`}
    />
  );
};

export default NavigationDropdown;
