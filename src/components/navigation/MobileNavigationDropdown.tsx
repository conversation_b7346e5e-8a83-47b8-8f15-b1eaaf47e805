/**
 * Mobile Navigation Dropdown Component
 * 
 * Touch-friendly accordion-style dropdown for mobile navigation
 * with smooth animations and proper accessibility.
 */

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

interface MobileNavigationDropdownProps {
  type: 'countries' | 'scholarships' | 'opportunities';
  label: string;
  onItemClick?: () => void;
}

interface CountryData {
  name: string;
  count: number;
  slug: string;
}

interface LevelData {
  name: string;
  count: number;
  openCount: number;
  slug: string;
}

interface OpportunityTypeData {
  name: string;
  count: number;
  activeCount: number;
  slug: string;
}

const MobileNavigationDropdown: React.FC<MobileNavigationDropdownProps> = ({
  type,
  label,
  onItemClick
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [items, setItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const { translations } = useLanguage();

  const fetchData = async () => {
    if (items.length > 0) return;
    
    setLoading(true);
    try {
      let endpoint = '';
      let dataProcessor: (data: any[]) => any[] = () => [];

      switch (type) {
        case 'countries':
          endpoint = '/api/countries';
          dataProcessor = (countries: CountryData[]) => [
            {
              id: 'all-countries',
              label: translations.navigation.allCountries || 'All Countries',
              href: '/countries',
              count: countries.reduce((sum, c) => sum + c.count, 0)
            },
            ...countries.slice(0, 6).map(country => ({
              id: country.slug,
              label: country.name,
              href: `/countries/${encodeURIComponent(country.name)}`,
              count: country.count
            }))
          ];
          break;

        case 'scholarships':
          endpoint = '/api/scholarships/levels';
          dataProcessor = (levels: LevelData[]) => [
            {
              id: 'all-scholarships',
              label: translations.navigation.allScholarships || 'All Scholarships',
              href: '/scholarships',
              count: levels.reduce((sum, l) => sum + l.count, 0)
            },
            ...levels.map(level => ({
              id: level.slug,
              label: level.name,
              href: `/scholarships?level=${encodeURIComponent(level.name)}`,
              count: level.openCount
            }))
          ];
          break;

        case 'opportunities':
          endpoint = '/api/opportunities/types';
          dataProcessor = (types: OpportunityTypeData[]) => [
            {
              id: 'all-opportunities',
              label: translations.navigation.allOpportunities || 'All Opportunities',
              href: '/opportunities',
              count: types.reduce((sum, t) => sum + t.count, 0)
            },
            ...types.map(opType => ({
              id: opType.slug,
              label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),
              href: `/opportunities?type=${encodeURIComponent(opType.name)}`,
              count: opType.activeCount
            }))
          ];
          break;
      }

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);
      if (!response.ok) throw new Error('Failed to fetch data');
      
      const result = await response.json();
      const data = result.data || result;
      
      setItems(dataProcessor(data));
    } catch (error) {
      console.error(`Error fetching ${type} data:`, error);
      setItems([{
        id: 'error',
        label: 'Failed to load data',
        href: `/${type}`
      }]);
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = () => {
    if (!isOpen && items.length === 0) {
      fetchData();
    }
    setIsOpen(!isOpen);
  };



  // Get the main page URL for this navigation type
  const getMainPageUrl = () => {
    switch (type) {
      case 'countries':
        return '/countries';
      case 'scholarships':
        return '/scholarships';
      case 'opportunities':
        return '/opportunities';
      default:
        return '/';
    }
  };

  return (
    <div className="border-b border-gray-200">
      {/* Main Navigation Item with Link and Toggle */}
      <div className="flex items-center">
        {/* Main clickable link */}
        <Link
          to={getMainPageUrl()}
          onClick={onItemClick}
          className="flex-1 px-4 py-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 transition-colors duration-200"
        >
          {label}
        </Link>

        {/* Dropdown toggle button */}
        <button
          onClick={handleToggle}
          className="px-4 py-3 text-gray-400 hover:text-primary transition-colors duration-200"
          aria-expanded={isOpen}
          aria-controls={`mobile-dropdown-${type}`}
          aria-label={`Toggle ${label} submenu`}
        >
          <ChevronDown
            size={18}
            className={`transition-transform duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`}
          />
        </button>
      </div>

      {/* Dropdown Content */}
      <div
        id={`mobile-dropdown-${type}`}
        className={`
          overflow-hidden transition-all duration-300 ease-in-out
          ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}
        `}
      >
        <div className="bg-gray-50 border-t border-gray-200">
          {loading ? (
            <div className="px-6 py-4 text-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary mx-auto mb-2"></div>
              <span className="text-sm text-gray-500">Loading...</span>
            </div>
          ) : items.length === 0 ? (
            <div className="px-6 py-4 text-center text-gray-500 text-sm">
              No items available
            </div>
          ) : (
            <div className="py-2">
              {items.map((item) => (
                <Link
                  key={item.id}
                  to={item.href}
                  onClick={onItemClick}
                  className="flex items-center justify-between px-6 py-3 text-sm text-gray-600 hover:text-primary hover:bg-white transition-colors duration-200"
                >
                  <span>{item.label}</span>
                  {item.count !== undefined && (
                    <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                      {item.count}
                    </span>
                  )}
                </Link>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MobileNavigationDropdown;
