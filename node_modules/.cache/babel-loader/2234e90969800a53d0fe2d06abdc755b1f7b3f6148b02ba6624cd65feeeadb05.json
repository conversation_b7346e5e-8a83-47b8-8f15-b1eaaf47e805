{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Outlet } from 'react-router-dom';\nimport Footer from './Footer';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = () => {\n  _s();\n  const {\n    direction\n  } = useLanguage();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen flex flex-col ${direction === 'rtl' ? 'rtl' : 'ltr'}`,\n    dir: direction,\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"flex-grow pt-16\",\n      children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"JIFaXz1s66+9b+vPIhEPn/9JMx8=\", false, function () {\n  return [useLanguage];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Outlet", "Footer", "useLanguage", "jsxDEV", "_jsxDEV", "Layout", "_s", "direction", "className", "dir", "children", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport { Outlet } from 'react-router-dom';\nimport EnhancedHeader from './EnhancedHeader';\nimport Footer from './Footer';\nimport { useLanguage } from '../../context/LanguageContext';\n\nconst Layout: React.FC = () => {\n  const { direction } = useLanguage();\n\n  return (\n    <div \n      className={`min-h-screen flex flex-col ${direction === 'rtl' ? 'rtl' : 'ltr'}`}\n      dir={direction}\n    >\n      <Header />\n      <main className=\"flex-grow pt-16\">\n        <Outlet />\n      </main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default Layout; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,kBAAkB;AAEzC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAU,CAAC,GAAGL,WAAW,CAAC,CAAC;EAEnC,oBACEE,OAAA;IACEI,SAAS,EAAE,8BAA8BD,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,EAAG;IAC/EE,GAAG,EAAEF,SAAU;IAAAG,QAAA,gBAEfN,OAAA,CAACO,MAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVX,OAAA;MAAMI,SAAS,EAAC,iBAAiB;MAAAE,QAAA,eAC/BN,OAAA,CAACJ,MAAM;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACPX,OAAA,CAACH,MAAM;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACT,EAAA,CAfID,MAAgB;EAAA,QACEH,WAAW;AAAA;AAAAc,EAAA,GAD7BX,MAAgB;AAiBtB,eAAeA,MAAM;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}