{"ast": null, "code": "/**\n * Production-Grade Image Utilities\n * \n * This module provides robust image handling utilities for the frontend\n * with proper error handling, fallbacks, and performance optimization.\n */\n\n// Image configuration constants\nexport const IMAGE_CONFIG = {\n  // API base URL\n  API_BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:5000',\n  // Default fallback images\n  DEFAULT_SCHOLARSHIP_IMAGE: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',\n  DEFAULT_PLACEHOLDER: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIyNSIgdmlld0JveD0iMCAwIDQwMCAyMjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjI1IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNzUgMTEyLjVMMTg3LjUgMTAwTDIwMCAxMTIuNUwyMTIuNSAxMDBMMjI1IDExMi41VjE0MEgxNzVWMTEyLjVaIiBmaWxsPSIjOUI5QkEwIi8+CjxjaXJjbGUgY3g9IjE4NyIgY3k9IjkwIiByPSI1IiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTIwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiM5QjlCQTAiPkltYWdlPC90ZXh0Pgo8L3N2Zz4K',\n  // Supported image formats\n  SUPPORTED_FORMATS: ['jpg', 'jpeg', 'png', 'webp'],\n  // Thumbnail sizes\n  THUMBNAIL_SIZES: {\n    small: {\n      width: 150,\n      height: 150\n    },\n    medium: {\n      width: 300,\n      height: 300\n    },\n    large: {\n      width: 600,\n      height: 400\n    },\n    card: {\n      width: 400,\n      height: 225\n    }\n  },\n  // Cache settings\n  CACHE_DURATION: 3600000,\n  // 1 hour in milliseconds\n  MAX_RETRY_ATTEMPTS: 3,\n  RETRY_DELAY: 1000,\n  // 1 second\n\n  // CDN settings\n  CDN_BASE_URL: process.env.REACT_APP_CDN_URL || '',\n  IMAGE_VERSION: process.env.REACT_APP_IMAGE_VERSION || 'v1'\n};\n\n// Image loading states\nexport let ImageLoadState = /*#__PURE__*/function (ImageLoadState) {\n  ImageLoadState[\"LOADING\"] = \"loading\";\n  ImageLoadState[\"LOADED\"] = \"loaded\";\n  ImageLoadState[\"ERROR\"] = \"error\";\n  ImageLoadState[\"FALLBACK\"] = \"fallback\";\n  return ImageLoadState;\n}({});\n\n// Image metadata interface\n\n// Image cache for performance\nclass ImageCache {\n  constructor() {\n    this.cache = new Map();\n  }\n  set(key, url, state) {\n    this.cache.set(key, {\n      url,\n      timestamp: Date.now(),\n      state\n    });\n  }\n  get(key) {\n    const cached = this.cache.get(key);\n    if (!cached) return null;\n\n    // Check if cache is expired\n    if (Date.now() - cached.timestamp > IMAGE_CONFIG.CACHE_DURATION) {\n      this.cache.delete(key);\n      return null;\n    }\n    return {\n      url: cached.url,\n      state: cached.state\n    };\n  }\n  clear() {\n    this.cache.clear();\n  }\n}\nconst imageCache = new ImageCache();\n\n/**\n * Construct proper image URL with fallback handling\n */\nexport function constructImageUrl(thumbnailPath, size = 'original') {\n  // Handle null/undefined/empty paths\n  if (!thumbnailPath || thumbnailPath.trim() === '') {\n    return IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;\n  }\n\n  // Handle absolute URLs (external images)\n  if (thumbnailPath.startsWith('http://') || thumbnailPath.startsWith('https://')) {\n    return thumbnailPath;\n  }\n\n  // Handle base64 data URLs\n  if (thumbnailPath.startsWith('data:')) {\n    return thumbnailPath;\n  }\n\n  // Construct CDN-ready URL for uploaded images\n  const baseUrl = IMAGE_CONFIG.CDN_BASE_URL || IMAGE_CONFIG.API_BASE_URL;\n  const version = IMAGE_CONFIG.IMAGE_VERSION;\n  let imageUrl;\n  if (size === 'original') {\n    // Original image with cache busting\n    imageUrl = `${baseUrl}${thumbnailPath}?v=${version}`;\n  } else {\n    // Optimized thumbnail version\n    const pathParts = thumbnailPath.split('/');\n    const filename = pathParts[pathParts.length - 1];\n    const filenameWithoutExt = filename.replace(/\\.[^/.]+$/, '');\n    const thumbnailFilename = `${filenameWithoutExt}_${size}.webp`;\n    imageUrl = `${baseUrl}/uploads/scholarships/thumbnails/${thumbnailFilename}?v=${version}`;\n  }\n  return imageUrl;\n}\n\n/**\n * Preload image with retry mechanism\n */\nexport function preloadImage(url, retryCount = 0) {\n  return new Promise(resolve => {\n    // Check cache first\n    const cached = imageCache.get(url);\n    if (cached) {\n      resolve(cached.state);\n      return;\n    }\n    const img = new Image();\n    img.onload = () => {\n      imageCache.set(url, url, ImageLoadState.LOADED);\n      resolve(ImageLoadState.LOADED);\n    };\n    img.onerror = () => {\n      if (retryCount < IMAGE_CONFIG.MAX_RETRY_ATTEMPTS) {\n        // Retry after delay\n        setTimeout(() => {\n          preloadImage(url, retryCount + 1).then(resolve);\n        }, IMAGE_CONFIG.RETRY_DELAY * (retryCount + 1));\n      } else {\n        imageCache.set(url, url, ImageLoadState.ERROR);\n        resolve(ImageLoadState.ERROR);\n      }\n    };\n    img.src = url;\n  });\n}\n\n/**\n * Get optimized image URL with fallback chain\n */\nexport async function getOptimizedImageUrl(thumbnailPath, size = 'card') {\n  // Try thumbnail first\n  const thumbnailUrl = constructImageUrl(thumbnailPath, size);\n  const thumbnailState = await preloadImage(thumbnailUrl);\n  if (thumbnailState === ImageLoadState.LOADED) {\n    return thumbnailUrl;\n  }\n\n  // Fallback to original image\n  const originalUrl = constructImageUrl(thumbnailPath, 'original');\n  const originalState = await preloadImage(originalUrl);\n  if (originalState === ImageLoadState.LOADED) {\n    return originalUrl;\n  }\n\n  // Final fallback to default image\n  return IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;\n}\n\n/**\n * Generate srcSet for responsive images with CDN support\n */\nexport function generateSrcSet(thumbnailPath) {\n  if (!thumbnailPath) {\n    return IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;\n  }\n  const sizes = ['small', 'medium', 'large'];\n  const srcSetEntries = sizes.map(size => {\n    const url = constructImageUrl(thumbnailPath, size);\n    const dimensions = IMAGE_CONFIG.THUMBNAIL_SIZES[size];\n    return `${url} ${dimensions.width}w`;\n  });\n  return srcSetEntries.join(', ');\n}\n\n/**\n * Get responsive image URLs for different breakpoints\n */\nexport function getResponsiveImageUrls(thumbnailPath) {\n  return {\n    mobile: constructImageUrl(thumbnailPath, 'small'),\n    tablet: constructImageUrl(thumbnailPath, 'medium'),\n    desktop: constructImageUrl(thumbnailPath, 'large'),\n    original: constructImageUrl(thumbnailPath, 'original')\n  };\n}\n\n/**\n * Generate sizes attribute for responsive images\n */\nexport function generateSizesAttribute() {\n  return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw';\n}\n\n/**\n * Get image metadata for SEO and accessibility\n */\nexport function getImageMetadata(thumbnailPath, title, size = 'card') {\n  const url = constructImageUrl(thumbnailPath, size);\n  const dimensions = IMAGE_CONFIG.THUMBNAIL_SIZES[size];\n  return {\n    url,\n    alt: `${title} - Scholarship thumbnail`,\n    width: dimensions.width,\n    height: dimensions.height,\n    format: 'webp',\n    size\n  };\n}\n\n/**\n * Validate image URL format\n */\nexport function isValidImageUrl(url) {\n  if (!url || typeof url !== 'string') return false;\n\n  // Check for valid URL patterns\n  const urlPattern = /^(https?:\\/\\/|data:|\\/)/;\n  if (!urlPattern.test(url)) return false;\n\n  // Check for supported formats (if it's a file URL)\n  if (url.includes('.')) {\n    var _url$split$pop;\n    const extension = (_url$split$pop = url.split('.').pop()) === null || _url$split$pop === void 0 ? void 0 : _url$split$pop.toLowerCase();\n    return extension ? IMAGE_CONFIG.SUPPORTED_FORMATS.includes(extension) : false;\n  }\n  return true;\n}\n\n/**\n * Handle image loading errors with graceful fallback\n */\nexport function handleImageError(event, fallbackUrl) {\n  const img = event.target;\n\n  // Prevent infinite loop\n  if (img.src === IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE) {\n    return;\n  }\n\n  // Try custom fallback first\n  if (fallbackUrl && img.src !== fallbackUrl) {\n    img.src = fallbackUrl;\n    return;\n  }\n\n  // Use default fallback\n  img.src = IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;\n}\n\n/**\n * Clear image cache (useful for memory management)\n */\nexport function clearImageCache() {\n  imageCache.clear();\n}\n\n/**\n * Get image loading placeholder\n */\nexport function getImagePlaceholder() {\n  return IMAGE_CONFIG.DEFAULT_PLACEHOLDER;\n}", "map": {"version": 3, "names": ["IMAGE_CONFIG", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "DEFAULT_SCHOLARSHIP_IMAGE", "DEFAULT_PLACEHOLDER", "SUPPORTED_FORMATS", "THUMBNAIL_SIZES", "small", "width", "height", "medium", "large", "card", "CACHE_DURATION", "MAX_RETRY_ATTEMPTS", "RETRY_DELAY", "CDN_BASE_URL", "REACT_APP_CDN_URL", "IMAGE_VERSION", "REACT_APP_IMAGE_VERSION", "ImageLoadState", "ImageCache", "constructor", "cache", "Map", "set", "key", "url", "state", "timestamp", "Date", "now", "get", "cached", "delete", "clear", "imageCache", "constructImageUrl", "thumbnail<PERSON>ath", "size", "trim", "startsWith", "baseUrl", "version", "imageUrl", "pathParts", "split", "filename", "length", "filenameWithoutExt", "replace", "thumbnailFilename", "preloadImage", "retryCount", "Promise", "resolve", "img", "Image", "onload", "LOADED", "onerror", "setTimeout", "then", "ERROR", "src", "getOptimizedImageUrl", "thumbnailUrl", "thumbnailState", "originalUrl", "originalState", "generateSrcSet", "sizes", "srcSetEntries", "map", "dimensions", "join", "getResponsiveImageUrls", "mobile", "tablet", "desktop", "original", "generateSizesAttribute", "getImageMetadata", "title", "alt", "format", "isValidImageUrl", "urlPattern", "test", "includes", "_url$split$pop", "extension", "pop", "toLowerCase", "handleImageError", "event", "fallbackUrl", "target", "clearImageCache", "getImagePlaceholder"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/imageUtils.ts"], "sourcesContent": ["/**\n * Production-Grade Image Utilities\n * \n * This module provides robust image handling utilities for the frontend\n * with proper error handling, fallbacks, and performance optimization.\n */\n\n// Image configuration constants\nexport const IMAGE_CONFIG = {\n  // API base URL\n  API_BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:5000',\n  \n  // Default fallback images\n  DEFAULT_SCHOLARSHIP_IMAGE: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',\n  DEFAULT_PLACEHOLDER: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIyNSIgdmlld0JveD0iMCAwIDQwMCAyMjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjI1IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNzUgMTEyLjVMMTg3LjUgMTAwTDIwMCAxMTIuNUwyMTIuNSAxMDBMMjI1IDExMi41VjE0MEgxNzVWMTEyLjVaIiBmaWxsPSIjOUI5QkEwIi8+CjxjaXJjbGUgY3g9IjE4NyIgY3k9IjkwIiByPSI1IiBmaWxsPSIjOUI5QkEwIi8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTIwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiM5QjlCQTAiPkltYWdlPC90ZXh0Pgo8L3N2Zz4K',\n  \n  // Supported image formats\n  SUPPORTED_FORMATS: ['jpg', 'jpeg', 'png', 'webp'],\n  \n  // Thumbnail sizes\n  THUMBNAIL_SIZES: {\n    small: { width: 150, height: 150 },\n    medium: { width: 300, height: 300 },\n    large: { width: 600, height: 400 },\n    card: { width: 400, height: 225 }\n  },\n  \n  // Cache settings\n  CACHE_DURATION: 3600000, // 1 hour in milliseconds\n  MAX_RETRY_ATTEMPTS: 3,\n  RETRY_DELAY: 1000, // 1 second\n\n  // CDN settings\n  CDN_BASE_URL: process.env.REACT_APP_CDN_URL || '',\n  IMAGE_VERSION: process.env.REACT_APP_IMAGE_VERSION || 'v1'\n};\n\n// Image loading states\nexport enum ImageLoadState {\n  LOADING = 'loading',\n  LOADED = 'loaded',\n  ERROR = 'error',\n  FALLBACK = 'fallback'\n}\n\n// Image metadata interface\nexport interface ImageMetadata {\n  url: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  format?: string;\n  size?: 'small' | 'medium' | 'large' | 'card';\n}\n\n// Image cache for performance\nclass ImageCache {\n  private cache = new Map<string, { url: string; timestamp: number; state: ImageLoadState }>();\n\n  set(key: string, url: string, state: ImageLoadState): void {\n    this.cache.set(key, {\n      url,\n      timestamp: Date.now(),\n      state\n    });\n  }\n\n  get(key: string): { url: string; state: ImageLoadState } | null {\n    const cached = this.cache.get(key);\n    if (!cached) return null;\n\n    // Check if cache is expired\n    if (Date.now() - cached.timestamp > IMAGE_CONFIG.CACHE_DURATION) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return { url: cached.url, state: cached.state };\n  }\n\n  clear(): void {\n    this.cache.clear();\n  }\n}\n\nconst imageCache = new ImageCache();\n\n/**\n * Construct proper image URL with fallback handling\n */\nexport function constructImageUrl(\n  thumbnailPath: string | null | undefined,\n  size: 'small' | 'medium' | 'large' | 'card' | 'original' = 'original'\n): string {\n  // Handle null/undefined/empty paths\n  if (!thumbnailPath || thumbnailPath.trim() === '') {\n    return IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;\n  }\n\n  // Handle absolute URLs (external images)\n  if (thumbnailPath.startsWith('http://') || thumbnailPath.startsWith('https://')) {\n    return thumbnailPath;\n  }\n\n  // Handle base64 data URLs\n  if (thumbnailPath.startsWith('data:')) {\n    return thumbnailPath;\n  }\n\n  // Construct CDN-ready URL for uploaded images\n  const baseUrl = IMAGE_CONFIG.CDN_BASE_URL || IMAGE_CONFIG.API_BASE_URL;\n  const version = IMAGE_CONFIG.IMAGE_VERSION;\n  let imageUrl: string;\n\n  if (size === 'original') {\n    // Original image with cache busting\n    imageUrl = `${baseUrl}${thumbnailPath}?v=${version}`;\n  } else {\n    // Optimized thumbnail version\n    const pathParts = thumbnailPath.split('/');\n    const filename = pathParts[pathParts.length - 1];\n    const filenameWithoutExt = filename.replace(/\\.[^/.]+$/, '');\n    const thumbnailFilename = `${filenameWithoutExt}_${size}.webp`;\n\n    imageUrl = `${baseUrl}/uploads/scholarships/thumbnails/${thumbnailFilename}?v=${version}`;\n  }\n\n  return imageUrl;\n}\n\n/**\n * Preload image with retry mechanism\n */\nexport function preloadImage(url: string, retryCount = 0): Promise<ImageLoadState> {\n  return new Promise((resolve) => {\n    // Check cache first\n    const cached = imageCache.get(url);\n    if (cached) {\n      resolve(cached.state);\n      return;\n    }\n\n    const img = new Image();\n    \n    img.onload = () => {\n      imageCache.set(url, url, ImageLoadState.LOADED);\n      resolve(ImageLoadState.LOADED);\n    };\n\n    img.onerror = () => {\n      if (retryCount < IMAGE_CONFIG.MAX_RETRY_ATTEMPTS) {\n        // Retry after delay\n        setTimeout(() => {\n          preloadImage(url, retryCount + 1).then(resolve);\n        }, IMAGE_CONFIG.RETRY_DELAY * (retryCount + 1));\n      } else {\n        imageCache.set(url, url, ImageLoadState.ERROR);\n        resolve(ImageLoadState.ERROR);\n      }\n    };\n\n    img.src = url;\n  });\n}\n\n/**\n * Get optimized image URL with fallback chain\n */\nexport async function getOptimizedImageUrl(\n  thumbnailPath: string | null | undefined,\n  size: 'small' | 'medium' | 'large' | 'card' = 'card'\n): Promise<string> {\n  // Try thumbnail first\n  const thumbnailUrl = constructImageUrl(thumbnailPath, size);\n  const thumbnailState = await preloadImage(thumbnailUrl);\n  \n  if (thumbnailState === ImageLoadState.LOADED) {\n    return thumbnailUrl;\n  }\n\n  // Fallback to original image\n  const originalUrl = constructImageUrl(thumbnailPath, 'original');\n  const originalState = await preloadImage(originalUrl);\n  \n  if (originalState === ImageLoadState.LOADED) {\n    return originalUrl;\n  }\n\n  // Final fallback to default image\n  return IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;\n}\n\n/**\n * Generate srcSet for responsive images with CDN support\n */\nexport function generateSrcSet(thumbnailPath: string | null | undefined): string {\n  if (!thumbnailPath) {\n    return IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;\n  }\n\n  const sizes = ['small', 'medium', 'large'] as const;\n  const srcSetEntries = sizes.map(size => {\n    const url = constructImageUrl(thumbnailPath, size);\n    const dimensions = IMAGE_CONFIG.THUMBNAIL_SIZES[size];\n    return `${url} ${dimensions.width}w`;\n  });\n\n  return srcSetEntries.join(', ');\n}\n\n/**\n * Get responsive image URLs for different breakpoints\n */\nexport function getResponsiveImageUrls(thumbnailPath: string | null | undefined): {\n  mobile: string;\n  tablet: string;\n  desktop: string;\n  original: string;\n} {\n  return {\n    mobile: constructImageUrl(thumbnailPath, 'small'),\n    tablet: constructImageUrl(thumbnailPath, 'medium'),\n    desktop: constructImageUrl(thumbnailPath, 'large'),\n    original: constructImageUrl(thumbnailPath, 'original')\n  };\n}\n\n/**\n * Generate sizes attribute for responsive images\n */\nexport function generateSizesAttribute(): string {\n  return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw';\n}\n\n/**\n * Get image metadata for SEO and accessibility\n */\nexport function getImageMetadata(\n  thumbnailPath: string | null | undefined,\n  title: string,\n  size: 'small' | 'medium' | 'large' | 'card' = 'card'\n): ImageMetadata {\n  const url = constructImageUrl(thumbnailPath, size);\n  const dimensions = IMAGE_CONFIG.THUMBNAIL_SIZES[size];\n  \n  return {\n    url,\n    alt: `${title} - Scholarship thumbnail`,\n    width: dimensions.width,\n    height: dimensions.height,\n    format: 'webp',\n    size\n  };\n}\n\n/**\n * Validate image URL format\n */\nexport function isValidImageUrl(url: string): boolean {\n  if (!url || typeof url !== 'string') return false;\n  \n  // Check for valid URL patterns\n  const urlPattern = /^(https?:\\/\\/|data:|\\/)/;\n  if (!urlPattern.test(url)) return false;\n  \n  // Check for supported formats (if it's a file URL)\n  if (url.includes('.')) {\n    const extension = url.split('.').pop()?.toLowerCase();\n    return extension ? IMAGE_CONFIG.SUPPORTED_FORMATS.includes(extension) : false;\n  }\n  \n  return true;\n}\n\n/**\n * Handle image loading errors with graceful fallback\n */\nexport function handleImageError(\n  event: React.SyntheticEvent<HTMLImageElement>,\n  fallbackUrl?: string\n): void {\n  const img = event.target as HTMLImageElement;\n  \n  // Prevent infinite loop\n  if (img.src === IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE) {\n    return;\n  }\n  \n  // Try custom fallback first\n  if (fallbackUrl && img.src !== fallbackUrl) {\n    img.src = fallbackUrl;\n    return;\n  }\n  \n  // Use default fallback\n  img.src = IMAGE_CONFIG.DEFAULT_SCHOLARSHIP_IMAGE;\n}\n\n/**\n * Clear image cache (useful for memory management)\n */\nexport function clearImageCache(): void {\n  imageCache.clear();\n}\n\n/**\n * Get image loading placeholder\n */\nexport function getImagePlaceholder(): string {\n  return IMAGE_CONFIG.DEFAULT_PLACEHOLDER;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,YAAY,GAAG;EAC1B;EACAC,YAAY,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EAEtE;EACAC,yBAAyB,EAAE,6GAA6G;EACxIC,mBAAmB,EAAE,whBAAwhB;EAE7iB;EACAC,iBAAiB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EAEjD;EACAC,eAAe,EAAE;IACfC,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC;IAClCC,MAAM,EAAE;MAAEF,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC;IACnCE,KAAK,EAAE;MAAEH,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC;IAClCG,IAAI,EAAE;MAAEJ,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI;EAClC,CAAC;EAED;EACAI,cAAc,EAAE,OAAO;EAAE;EACzBC,kBAAkB,EAAE,CAAC;EACrBC,WAAW,EAAE,IAAI;EAAE;;EAEnB;EACAC,YAAY,EAAEhB,OAAO,CAACC,GAAG,CAACgB,iBAAiB,IAAI,EAAE;EACjDC,aAAa,EAAElB,OAAO,CAACC,GAAG,CAACkB,uBAAuB,IAAI;AACxD,CAAC;;AAED;AACA,WAAYC,cAAc,0BAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAdA,cAAc;EAAA,OAAdA,cAAc;AAAA;;AAO1B;;AAUA;AACA,MAAMC,UAAU,CAAC;EAAAC,YAAA;IAAA,KACPC,KAAK,GAAG,IAAIC,GAAG,CAAoE,CAAC;EAAA;EAE5FC,GAAGA,CAACC,GAAW,EAAEC,GAAW,EAAEC,KAAqB,EAAQ;IACzD,IAAI,CAACL,KAAK,CAACE,GAAG,CAACC,GAAG,EAAE;MAClBC,GAAG;MACHE,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACrBH;IACF,CAAC,CAAC;EACJ;EAEAI,GAAGA,CAACN,GAAW,EAAiD;IAC9D,MAAMO,MAAM,GAAG,IAAI,CAACV,KAAK,CAACS,GAAG,CAACN,GAAG,CAAC;IAClC,IAAI,CAACO,MAAM,EAAE,OAAO,IAAI;;IAExB;IACA,IAAIH,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGE,MAAM,CAACJ,SAAS,GAAG/B,YAAY,CAACe,cAAc,EAAE;MAC/D,IAAI,CAACU,KAAK,CAACW,MAAM,CAACR,GAAG,CAAC;MACtB,OAAO,IAAI;IACb;IAEA,OAAO;MAAEC,GAAG,EAAEM,MAAM,CAACN,GAAG;MAAEC,KAAK,EAAEK,MAAM,CAACL;IAAM,CAAC;EACjD;EAEAO,KAAKA,CAAA,EAAS;IACZ,IAAI,CAACZ,KAAK,CAACY,KAAK,CAAC,CAAC;EACpB;AACF;AAEA,MAAMC,UAAU,GAAG,IAAIf,UAAU,CAAC,CAAC;;AAEnC;AACA;AACA;AACA,OAAO,SAASgB,iBAAiBA,CAC/BC,aAAwC,EACxCC,IAAwD,GAAG,UAAU,EAC7D;EACR;EACA,IAAI,CAACD,aAAa,IAAIA,aAAa,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACjD,OAAO1C,YAAY,CAACK,yBAAyB;EAC/C;;EAEA;EACA,IAAImC,aAAa,CAACG,UAAU,CAAC,SAAS,CAAC,IAAIH,aAAa,CAACG,UAAU,CAAC,UAAU,CAAC,EAAE;IAC/E,OAAOH,aAAa;EACtB;;EAEA;EACA,IAAIA,aAAa,CAACG,UAAU,CAAC,OAAO,CAAC,EAAE;IACrC,OAAOH,aAAa;EACtB;;EAEA;EACA,MAAMI,OAAO,GAAG5C,YAAY,CAACkB,YAAY,IAAIlB,YAAY,CAACC,YAAY;EACtE,MAAM4C,OAAO,GAAG7C,YAAY,CAACoB,aAAa;EAC1C,IAAI0B,QAAgB;EAEpB,IAAIL,IAAI,KAAK,UAAU,EAAE;IACvB;IACAK,QAAQ,GAAG,GAAGF,OAAO,GAAGJ,aAAa,MAAMK,OAAO,EAAE;EACtD,CAAC,MAAM;IACL;IACA,MAAME,SAAS,GAAGP,aAAa,CAACQ,KAAK,CAAC,GAAG,CAAC;IAC1C,MAAMC,QAAQ,GAAGF,SAAS,CAACA,SAAS,CAACG,MAAM,GAAG,CAAC,CAAC;IAChD,MAAMC,kBAAkB,GAAGF,QAAQ,CAACG,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IAC5D,MAAMC,iBAAiB,GAAG,GAAGF,kBAAkB,IAAIV,IAAI,OAAO;IAE9DK,QAAQ,GAAG,GAAGF,OAAO,oCAAoCS,iBAAiB,MAAMR,OAAO,EAAE;EAC3F;EAEA,OAAOC,QAAQ;AACjB;;AAEA;AACA;AACA;AACA,OAAO,SAASQ,YAAYA,CAACzB,GAAW,EAAE0B,UAAU,GAAG,CAAC,EAA2B;EACjF,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC9B;IACA,MAAMtB,MAAM,GAAGG,UAAU,CAACJ,GAAG,CAACL,GAAG,CAAC;IAClC,IAAIM,MAAM,EAAE;MACVsB,OAAO,CAACtB,MAAM,CAACL,KAAK,CAAC;MACrB;IACF;IAEA,MAAM4B,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IAEvBD,GAAG,CAACE,MAAM,GAAG,MAAM;MACjBtB,UAAU,CAACX,GAAG,CAACE,GAAG,EAAEA,GAAG,EAAEP,cAAc,CAACuC,MAAM,CAAC;MAC/CJ,OAAO,CAACnC,cAAc,CAACuC,MAAM,CAAC;IAChC,CAAC;IAEDH,GAAG,CAACI,OAAO,GAAG,MAAM;MAClB,IAAIP,UAAU,GAAGvD,YAAY,CAACgB,kBAAkB,EAAE;QAChD;QACA+C,UAAU,CAAC,MAAM;UACfT,YAAY,CAACzB,GAAG,EAAE0B,UAAU,GAAG,CAAC,CAAC,CAACS,IAAI,CAACP,OAAO,CAAC;QACjD,CAAC,EAAEzD,YAAY,CAACiB,WAAW,IAAIsC,UAAU,GAAG,CAAC,CAAC,CAAC;MACjD,CAAC,MAAM;QACLjB,UAAU,CAACX,GAAG,CAACE,GAAG,EAAEA,GAAG,EAAEP,cAAc,CAAC2C,KAAK,CAAC;QAC9CR,OAAO,CAACnC,cAAc,CAAC2C,KAAK,CAAC;MAC/B;IACF,CAAC;IAEDP,GAAG,CAACQ,GAAG,GAAGrC,GAAG;EACf,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,OAAO,eAAesC,oBAAoBA,CACxC3B,aAAwC,EACxCC,IAA2C,GAAG,MAAM,EACnC;EACjB;EACA,MAAM2B,YAAY,GAAG7B,iBAAiB,CAACC,aAAa,EAAEC,IAAI,CAAC;EAC3D,MAAM4B,cAAc,GAAG,MAAMf,YAAY,CAACc,YAAY,CAAC;EAEvD,IAAIC,cAAc,KAAK/C,cAAc,CAACuC,MAAM,EAAE;IAC5C,OAAOO,YAAY;EACrB;;EAEA;EACA,MAAME,WAAW,GAAG/B,iBAAiB,CAACC,aAAa,EAAE,UAAU,CAAC;EAChE,MAAM+B,aAAa,GAAG,MAAMjB,YAAY,CAACgB,WAAW,CAAC;EAErD,IAAIC,aAAa,KAAKjD,cAAc,CAACuC,MAAM,EAAE;IAC3C,OAAOS,WAAW;EACpB;;EAEA;EACA,OAAOtE,YAAY,CAACK,yBAAyB;AAC/C;;AAEA;AACA;AACA;AACA,OAAO,SAASmE,cAAcA,CAAChC,aAAwC,EAAU;EAC/E,IAAI,CAACA,aAAa,EAAE;IAClB,OAAOxC,YAAY,CAACK,yBAAyB;EAC/C;EAEA,MAAMoE,KAAK,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAU;EACnD,MAAMC,aAAa,GAAGD,KAAK,CAACE,GAAG,CAAClC,IAAI,IAAI;IACtC,MAAMZ,GAAG,GAAGU,iBAAiB,CAACC,aAAa,EAAEC,IAAI,CAAC;IAClD,MAAMmC,UAAU,GAAG5E,YAAY,CAACQ,eAAe,CAACiC,IAAI,CAAC;IACrD,OAAO,GAAGZ,GAAG,IAAI+C,UAAU,CAAClE,KAAK,GAAG;EACtC,CAAC,CAAC;EAEF,OAAOgE,aAAa,CAACG,IAAI,CAAC,IAAI,CAAC;AACjC;;AAEA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACtC,aAAwC,EAK7E;EACA,OAAO;IACLuC,MAAM,EAAExC,iBAAiB,CAACC,aAAa,EAAE,OAAO,CAAC;IACjDwC,MAAM,EAAEzC,iBAAiB,CAACC,aAAa,EAAE,QAAQ,CAAC;IAClDyC,OAAO,EAAE1C,iBAAiB,CAACC,aAAa,EAAE,OAAO,CAAC;IAClD0C,QAAQ,EAAE3C,iBAAiB,CAACC,aAAa,EAAE,UAAU;EACvD,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAAS2C,sBAAsBA,CAAA,EAAW;EAC/C,OAAO,0DAA0D;AACnE;;AAEA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAC9B5C,aAAwC,EACxC6C,KAAa,EACb5C,IAA2C,GAAG,MAAM,EACrC;EACf,MAAMZ,GAAG,GAAGU,iBAAiB,CAACC,aAAa,EAAEC,IAAI,CAAC;EAClD,MAAMmC,UAAU,GAAG5E,YAAY,CAACQ,eAAe,CAACiC,IAAI,CAAC;EAErD,OAAO;IACLZ,GAAG;IACHyD,GAAG,EAAE,GAAGD,KAAK,0BAA0B;IACvC3E,KAAK,EAAEkE,UAAU,CAAClE,KAAK;IACvBC,MAAM,EAAEiE,UAAU,CAACjE,MAAM;IACzB4E,MAAM,EAAE,MAAM;IACd9C;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAAS+C,eAAeA,CAAC3D,GAAW,EAAW;EACpD,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAO,KAAK;;EAEjD;EACA,MAAM4D,UAAU,GAAG,yBAAyB;EAC5C,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC7D,GAAG,CAAC,EAAE,OAAO,KAAK;;EAEvC;EACA,IAAIA,GAAG,CAAC8D,QAAQ,CAAC,GAAG,CAAC,EAAE;IAAA,IAAAC,cAAA;IACrB,MAAMC,SAAS,IAAAD,cAAA,GAAG/D,GAAG,CAACmB,KAAK,CAAC,GAAG,CAAC,CAAC8C,GAAG,CAAC,CAAC,cAAAF,cAAA,uBAApBA,cAAA,CAAsBG,WAAW,CAAC,CAAC;IACrD,OAAOF,SAAS,GAAG7F,YAAY,CAACO,iBAAiB,CAACoF,QAAQ,CAACE,SAAS,CAAC,GAAG,KAAK;EAC/E;EAEA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA,OAAO,SAASG,gBAAgBA,CAC9BC,KAA6C,EAC7CC,WAAoB,EACd;EACN,MAAMxC,GAAG,GAAGuC,KAAK,CAACE,MAA0B;;EAE5C;EACA,IAAIzC,GAAG,CAACQ,GAAG,KAAKlE,YAAY,CAACK,yBAAyB,EAAE;IACtD;EACF;;EAEA;EACA,IAAI6F,WAAW,IAAIxC,GAAG,CAACQ,GAAG,KAAKgC,WAAW,EAAE;IAC1CxC,GAAG,CAACQ,GAAG,GAAGgC,WAAW;IACrB;EACF;;EAEA;EACAxC,GAAG,CAACQ,GAAG,GAAGlE,YAAY,CAACK,yBAAyB;AAClD;;AAEA;AACA;AACA;AACA,OAAO,SAAS+F,eAAeA,CAAA,EAAS;EACtC9D,UAAU,CAACD,KAAK,CAAC,CAAC;AACpB;;AAEA;AACA;AACA;AACA,OAAO,SAASgE,mBAAmBA,CAAA,EAAW;EAC5C,OAAOrG,YAAY,CAACM,mBAAmB;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}