{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { calculateDaysRemaining } from '../utils/dateFormatter';\nimport { constructImageUrl, handleImageError, getImagePlaceholder, preloadImageWithRetry, ImageLoadState, reportImageError } from '../utils/imageUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScholarshipCard = ({\n  id,\n  title,\n  thumbnail,\n  deadline,\n  isOpen,\n  onClick,\n  level,\n  country\n}) => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(getImagePlaceholder());\n  const [imageState, setImageState] = useState(ImageLoadState.LOADING);\n  const {\n    formattedText,\n    isOpen: isNotExpired\n  } = calculateDaysRemaining(deadline);\n\n  // Use the calculated isOpen status if available, otherwise use the prop\n  const scholarshipStatus = isNotExpired !== undefined ? isNotExpired : isOpen;\n\n  // Load optimized image\n  useEffect(() => {\n    const loadImage = async () => {\n      setImageState(ImageLoadState.LOADING);\n      try {\n        // Try card-sized thumbnail first with enhanced error handling\n        const cardUrl = constructImageUrl(thumbnail, 'card');\n        const cardState = await preloadImageWithRetry(cardUrl, 0, progress => {\n          if (progress.error) {\n            reportImageError(cardUrl, progress.error);\n          }\n        });\n        if (cardState === ImageLoadState.LOADED) {\n          setImageUrl(cardUrl);\n          setImageState(ImageLoadState.LOADED);\n          return;\n        }\n\n        // Fallback to original image\n        const originalUrl = constructImageUrl(thumbnail, 'original');\n        const originalState = await preloadImageWithRetry(originalUrl, 0, progress => {\n          if (progress.error) {\n            reportImageError(originalUrl, progress.error);\n          }\n        });\n        if (originalState === ImageLoadState.LOADED) {\n          setImageUrl(originalUrl);\n          setImageState(ImageLoadState.LOADED);\n          return;\n        }\n\n        // Final fallback\n        setImageUrl(constructImageUrl(null));\n        setImageState(ImageLoadState.FALLBACK);\n      } catch (error) {\n        const errorMessage = `Error loading scholarship image: ${error.message}`;\n        console.error(errorMessage);\n        reportImageError(thumbnail || 'unknown', errorMessage);\n        setImageUrl(constructImageUrl(null));\n        setImageState(ImageLoadState.ERROR);\n      }\n    };\n    loadImage();\n  }, [thumbnail]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"group bg-white rounded-xl shadow-sm overflow-hidden w-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border border-gray-100 flex flex-col cursor-pointer\",\n    onClick: () => onClick(id),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-full aspect-[16/9] overflow-hidden\",\n      children: [imageState === ImageLoadState.LOADING && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-full bg-gray-200 animate-pulse flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-400 text-sm\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: `${title} - Scholarship thumbnail`,\n        className: `w-full h-full object-cover transform transition-all duration-500 group-hover:scale-105 ${imageState === ImageLoadState.LOADING ? 'opacity-0' : 'opacity-100'}`,\n        loading: \"lazy\",\n        onLoad: () => setImageState(ImageLoadState.LOADED),\n        onError: e => handleImageError(e, constructImageUrl(null))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), level && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-3 left-3 z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `px-2 py-1 rounded-full text-xs font-medium bg-white/80 backdrop-blur-sm shadow-sm ${level === 'Licence' ? 'text-blue-700' : level === 'Master' ? 'text-purple-700' : 'text-indigo-700'}`,\n          children: level\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this), country && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 bg-gradient-to-r from-black/70 to-transparent w-full py-2 px-3\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-white text-xs font-medium\",\n          children: country\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 flex-grow flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-base font-bold text-gray-900 line-clamp-2 mb-3 group-hover:text-primary transition-colors duration-200\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-4 w-4 mr-1.5 text-gray-400\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm font-medium ${!scholarshipStatus ? 'text-red-600' : formattedText.includes('jour') ? 'text-amber-600' : 'text-gray-600'}`,\n            children: formattedText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${scholarshipStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n          children: scholarshipStatus ? '✅ Ouvert' : '❌ Fermé'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(ScholarshipCard, \"PC9Rmn0OFgPp7hx08A1/E9shZ5Q=\");\n_c = ScholarshipCard;\nexport default ScholarshipCard;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "calculateDaysRemaining", "constructImageUrl", "handleImageError", "getImagePlaceholder", "preloadImageWithRetry", "ImageLoadState", "reportImageError", "jsxDEV", "_jsxDEV", "ScholarshipCard", "id", "title", "thumbnail", "deadline", "isOpen", "onClick", "level", "country", "_s", "imageUrl", "setImageUrl", "imageState", "setImageState", "LOADING", "formattedText", "isNotExpired", "scholarshipStatus", "undefined", "loadImage", "cardUrl", "cardState", "progress", "error", "LOADED", "originalUrl", "originalState", "FALLBACK", "errorMessage", "message", "console", "ERROR", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "loading", "onLoad", "onError", "e", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "includes", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { calculateDaysRemaining } from '../utils/dateFormatter';\nimport { constructImageUrl, handleImageError, getImagePlaceholder, preloadImageWithRetry, ImageLoadState, reportImageError } from '../utils/imageUtils';\n\ninterface ScholarshipCardProps {\n  id: number;\n  title: string;\n  thumbnail: string;\n  deadline: string;\n  isOpen: boolean;\n  onClick: (id: number) => void;\n  level?: string;\n  fundingSource?: string;\n  country?: string;\n}\n\nconst ScholarshipCard: React.FC<ScholarshipCardProps> = ({\n  id,\n  title,\n  thumbnail,\n  deadline,\n  isOpen,\n  onClick,\n  level,\n  country,\n}) => {\n  const [imageUrl, setImageUrl] = useState<string>(getImagePlaceholder());\n  const [imageState, setImageState] = useState<ImageLoadState>(ImageLoadState.LOADING);\n\n  const { formattedText, isOpen: isNotExpired } = calculateDaysRemaining(deadline);\n\n  // Use the calculated isOpen status if available, otherwise use the prop\n  const scholarshipStatus = isNotExpired !== undefined ? isNotExpired : isOpen;\n\n  // Load optimized image\n  useEffect(() => {\n    const loadImage = async () => {\n      setImageState(ImageLoadState.LOADING);\n\n      try {\n        // Try card-sized thumbnail first with enhanced error handling\n        const cardUrl = constructImageUrl(thumbnail, 'card');\n        const cardState = await preloadImageWithRetry(cardUrl, 0, (progress) => {\n          if (progress.error) {\n            reportImageError(cardUrl, progress.error);\n          }\n        });\n\n        if (cardState === ImageLoadState.LOADED) {\n          setImageUrl(cardUrl);\n          setImageState(ImageLoadState.LOADED);\n          return;\n        }\n\n        // Fallback to original image\n        const originalUrl = constructImageUrl(thumbnail, 'original');\n        const originalState = await preloadImageWithRetry(originalUrl, 0, (progress) => {\n          if (progress.error) {\n            reportImageError(originalUrl, progress.error);\n          }\n        });\n\n        if (originalState === ImageLoadState.LOADED) {\n          setImageUrl(originalUrl);\n          setImageState(ImageLoadState.LOADED);\n          return;\n        }\n\n        // Final fallback\n        setImageUrl(constructImageUrl(null));\n        setImageState(ImageLoadState.FALLBACK);\n      } catch (error) {\n        const errorMessage = `Error loading scholarship image: ${(error as Error).message}`;\n        console.error(errorMessage);\n        reportImageError(thumbnail || 'unknown', errorMessage);\n        setImageUrl(constructImageUrl(null));\n        setImageState(ImageLoadState.ERROR);\n      }\n    };\n\n    loadImage();\n  }, [thumbnail]);\n\n  return (\n    <div\n      className=\"group bg-white rounded-xl shadow-sm overflow-hidden w-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border border-gray-100 flex flex-col cursor-pointer\"\n      onClick={() => onClick(id)}\n    >\n      {/* Thumbnail - Fixed aspect ratio for consistency */}\n      <div className=\"relative w-full aspect-[16/9] overflow-hidden\">\n        {imageState === ImageLoadState.LOADING && (\n          <div className=\"w-full h-full bg-gray-200 animate-pulse flex items-center justify-center\">\n            <div className=\"text-gray-400 text-sm\">Loading...</div>\n          </div>\n        )}\n        <img\n          src={imageUrl}\n          alt={`${title} - Scholarship thumbnail`}\n          className={`w-full h-full object-cover transform transition-all duration-500 group-hover:scale-105 ${\n            imageState === ImageLoadState.LOADING ? 'opacity-0' : 'opacity-100'\n          }`}\n          loading=\"lazy\"\n          onLoad={() => setImageState(ImageLoadState.LOADED)}\n          onError={(e) => handleImageError(e, constructImageUrl(null))}\n        />\n\n        {/* Level badge if available */}\n        {level && (\n          <div className=\"absolute top-3 left-3 z-10\">\n            <span className={`px-2 py-1 rounded-full text-xs font-medium bg-white/80 backdrop-blur-sm shadow-sm ${\n              level === 'Licence' ? 'text-blue-700' :\n              level === 'Master' ? 'text-purple-700' :\n              'text-indigo-700'\n            }`}>\n              {level}\n            </span>\n          </div>\n        )}\n\n        {/* Country overlay */}\n        {country && (\n          <div className=\"absolute bottom-0 left-0 bg-gradient-to-r from-black/70 to-transparent w-full py-2 px-3\">\n            <span className=\"text-white text-xs font-medium\">{country}</span>\n          </div>\n        )}\n      </div>\n\n      {/* Content area */}\n      <div className=\"p-4 flex-grow flex flex-col\">\n        {/* Title - 2 lines max with ellipsis */}\n        <h3 className=\"text-base font-bold text-gray-900 line-clamp-2 mb-3 group-hover:text-primary transition-colors duration-200\">\n          {title}\n        </h3>\n\n        {/* Deadline and status */}\n        <div className=\"mt-auto flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <svg className=\"h-4 w-4 mr-1.5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n            </svg>\n            <span className={`text-sm font-medium ${\n              !scholarshipStatus ? 'text-red-600' :\n              formattedText.includes('jour') ? 'text-amber-600' : 'text-gray-600'\n            }`}>\n              {formattedText}\n            </span>\n          </div>\n\n          {/* Status badge */}\n          <span\n            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${\n              scholarshipStatus\n                ? 'bg-green-100 text-green-800'\n                : 'bg-red-100 text-red-800'\n            }`}\n          >\n            {scholarshipStatus ? '✅ Ouvert' : '❌ Fermé'}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ScholarshipCard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,sBAAsB,QAAQ,wBAAwB;AAC/D,SAASC,iBAAiB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAcxJ,MAAMC,eAA+C,GAAGA,CAAC;EACvDC,EAAE;EACFC,KAAK;EACLC,SAAS;EACTC,QAAQ;EACRC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAASK,mBAAmB,CAAC,CAAC,CAAC;EACvE,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAiBO,cAAc,CAACkB,OAAO,CAAC;EAEpF,MAAM;IAAEC,aAAa;IAAEV,MAAM,EAAEW;EAAa,CAAC,GAAGzB,sBAAsB,CAACa,QAAQ,CAAC;;EAEhF;EACA,MAAMa,iBAAiB,GAAGD,YAAY,KAAKE,SAAS,GAAGF,YAAY,GAAGX,MAAM;;EAE5E;EACAf,SAAS,CAAC,MAAM;IACd,MAAM6B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BN,aAAa,CAACjB,cAAc,CAACkB,OAAO,CAAC;MAErC,IAAI;QACF;QACA,MAAMM,OAAO,GAAG5B,iBAAiB,CAACW,SAAS,EAAE,MAAM,CAAC;QACpD,MAAMkB,SAAS,GAAG,MAAM1B,qBAAqB,CAACyB,OAAO,EAAE,CAAC,EAAGE,QAAQ,IAAK;UACtE,IAAIA,QAAQ,CAACC,KAAK,EAAE;YAClB1B,gBAAgB,CAACuB,OAAO,EAAEE,QAAQ,CAACC,KAAK,CAAC;UAC3C;QACF,CAAC,CAAC;QAEF,IAAIF,SAAS,KAAKzB,cAAc,CAAC4B,MAAM,EAAE;UACvCb,WAAW,CAACS,OAAO,CAAC;UACpBP,aAAa,CAACjB,cAAc,CAAC4B,MAAM,CAAC;UACpC;QACF;;QAEA;QACA,MAAMC,WAAW,GAAGjC,iBAAiB,CAACW,SAAS,EAAE,UAAU,CAAC;QAC5D,MAAMuB,aAAa,GAAG,MAAM/B,qBAAqB,CAAC8B,WAAW,EAAE,CAAC,EAAGH,QAAQ,IAAK;UAC9E,IAAIA,QAAQ,CAACC,KAAK,EAAE;YAClB1B,gBAAgB,CAAC4B,WAAW,EAAEH,QAAQ,CAACC,KAAK,CAAC;UAC/C;QACF,CAAC,CAAC;QAEF,IAAIG,aAAa,KAAK9B,cAAc,CAAC4B,MAAM,EAAE;UAC3Cb,WAAW,CAACc,WAAW,CAAC;UACxBZ,aAAa,CAACjB,cAAc,CAAC4B,MAAM,CAAC;UACpC;QACF;;QAEA;QACAb,WAAW,CAACnB,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACpCqB,aAAa,CAACjB,cAAc,CAAC+B,QAAQ,CAAC;MACxC,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACd,MAAMK,YAAY,GAAG,oCAAqCL,KAAK,CAAWM,OAAO,EAAE;QACnFC,OAAO,CAACP,KAAK,CAACK,YAAY,CAAC;QAC3B/B,gBAAgB,CAACM,SAAS,IAAI,SAAS,EAAEyB,YAAY,CAAC;QACtDjB,WAAW,CAACnB,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACpCqB,aAAa,CAACjB,cAAc,CAACmC,KAAK,CAAC;MACrC;IACF,CAAC;IAEDZ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAChB,SAAS,CAAC,CAAC;EAEf,oBACEJ,OAAA;IACEiC,SAAS,EAAC,iLAAiL;IAC3L1B,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAACL,EAAE,CAAE;IAAAgC,QAAA,gBAG3BlC,OAAA;MAAKiC,SAAS,EAAC,+CAA+C;MAAAC,QAAA,GAC3DrB,UAAU,KAAKhB,cAAc,CAACkB,OAAO,iBACpCf,OAAA;QAAKiC,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvFlC,OAAA;UAAKiC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACN,eACDtC,OAAA;QACEuC,GAAG,EAAE5B,QAAS;QACd6B,GAAG,EAAE,GAAGrC,KAAK,0BAA2B;QACxC8B,SAAS,EAAE,0FACTpB,UAAU,KAAKhB,cAAc,CAACkB,OAAO,GAAG,WAAW,GAAG,aAAa,EAClE;QACH0B,OAAO,EAAC,MAAM;QACdC,MAAM,EAAEA,CAAA,KAAM5B,aAAa,CAACjB,cAAc,CAAC4B,MAAM,CAAE;QACnDkB,OAAO,EAAGC,CAAC,IAAKlD,gBAAgB,CAACkD,CAAC,EAAEnD,iBAAiB,CAAC,IAAI,CAAC;MAAE;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,EAGD9B,KAAK,iBACJR,OAAA;QAAKiC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzClC,OAAA;UAAMiC,SAAS,EAAE,qFACfzB,KAAK,KAAK,SAAS,GAAG,eAAe,GACrCA,KAAK,KAAK,QAAQ,GAAG,iBAAiB,GACtC,iBAAiB,EAChB;UAAA0B,QAAA,EACA1B;QAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGA7B,OAAO,iBACNT,OAAA;QAAKiC,SAAS,EAAC,yFAAyF;QAAAC,QAAA,eACtGlC,OAAA;UAAMiC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAEzB;QAAO;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1ClC,OAAA;QAAIiC,SAAS,EAAC,6GAA6G;QAAAC,QAAA,EACxH/B;MAAK;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGLtC,OAAA;QAAKiC,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDlC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClC,OAAA;YAAKiC,SAAS,EAAC,8BAA8B;YAACY,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAb,QAAA,eACjGlC,OAAA;cAAMgD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAwF;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7J,CAAC,eACNtC,OAAA;YAAMiC,SAAS,EAAE,uBACf,CAACf,iBAAiB,GAAG,cAAc,GACnCF,aAAa,CAACoC,QAAQ,CAAC,MAAM,CAAC,GAAG,gBAAgB,GAAG,eAAe,EAClE;YAAAlB,QAAA,EACAlB;UAAa;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNtC,OAAA;UACEiC,SAAS,EAAE,yEACTf,iBAAiB,GACb,6BAA6B,GAC7B,yBAAyB,EAC5B;UAAAgB,QAAA,EAEFhB,iBAAiB,GAAG,UAAU,GAAG;QAAS;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAlJIT,eAA+C;AAAAoD,EAAA,GAA/CpD,eAA+C;AAoJrD,eAAeA,eAAe;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}