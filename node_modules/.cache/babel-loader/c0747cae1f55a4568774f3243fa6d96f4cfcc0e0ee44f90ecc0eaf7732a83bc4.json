{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx\",\n  _s = $RefreshSig$();\n/**\n * Navigation Dropdown Component\n * \n * Specialized dropdown for navigation menus with data fetching,\n * proper routing, and professional styling.\n */\n\nimport React, { useState } from 'react';\nimport { ChevronDown, MapPin, GraduationCap, Briefcase } from 'lucide-react';\nimport Dropdown from '../common/Dropdown';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NavigationDropdown = ({\n  type,\n  label,\n  className = ''\n}) => {\n  _s();\n  const [items, setItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const {\n    translations\n  } = useLanguage();\n\n  // Fetch data when dropdown opens\n  const fetchData = async () => {\n    if (items.length > 0) return; // Don't refetch if we already have data\n\n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor = () => [];\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = countries => [{\n            id: 'all-countries',\n            label: translations.navigation.allCountries || 'All Countries',\n            href: '/countries',\n            icon: /*#__PURE__*/_jsxDEV(MapPin, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 21\n            }, this),\n            count: countries.reduce((sum, c) => sum + c.count, 0)\n          }, ...countries.slice(0, 8).map(country => ({\n            id: country.slug,\n            label: country.name,\n            href: `/countries/${encodeURIComponent(country.name)}`,\n            count: country.count\n          })), ...(countries.length > 8 ? [{\n            id: 'view-all-countries',\n            label: translations.navigation.viewAll || 'View All',\n            href: '/countries'\n          }] : [])];\n          break;\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = levels => [{\n            id: 'all-scholarships',\n            label: translations.navigation.allScholarships || 'All Scholarships',\n            href: '/scholarships',\n            icon: /*#__PURE__*/_jsxDEV(GraduationCap, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 21\n            }, this),\n            count: levels.reduce((sum, l) => sum + l.count, 0)\n          }, ...levels.map(level => ({\n            id: level.slug,\n            label: level.name,\n            href: `/scholarships?level=${encodeURIComponent(level.name)}`,\n            count: level.openCount\n          }))];\n          break;\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = types => [{\n            id: 'all-opportunities',\n            label: translations.navigation.allOpportunities || 'All Opportunities',\n            href: '/opportunities',\n            icon: /*#__PURE__*/_jsxDEV(Briefcase, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 21\n            }, this),\n            count: types.reduce((sum, t) => sum + t.count, 0)\n          }, ...types.map(opType => ({\n            id: opType.slug,\n            label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n            href: `/opportunities?type=${encodeURIComponent(opType.name)}`,\n            count: opType.activeCount\n          }))];\n          break;\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      const result = await response.json();\n      const data = result.data || result;\n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        disabled: true\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getIcon = () => {\n    switch (type) {\n      case 'countries':\n        return /*#__PURE__*/_jsxDEV(MapPin, {\n          size: 16,\n          className: \"text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 16\n        }, this);\n      case 'scholarships':\n        return /*#__PURE__*/_jsxDEV(GraduationCap, {\n          size: 16,\n          className: \"text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 16\n        }, this);\n      case 'opportunities':\n        return /*#__PURE__*/_jsxDEV(Briefcase, {\n          size: 16,\n          className: \"text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const trigger = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n      transition-all duration-300 ease-in-out\n      text-gray-700 hover:text-primary hover:bg-primary/5 hover:shadow-sm\n      group-hover:scale-105 transform\n      ${className}\n    `,\n    children: [getIcon(), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"transition-all duration-200\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n      size: 16,\n      className: \"text-gray-400 transition-all duration-300 ease-out group-hover:rotate-180 group-hover:text-primary\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Dropdown, {\n    trigger: trigger,\n    items: items,\n    loading: loading,\n    onOpen: fetchData,\n    showOnHover: true,\n    closeOnClick: true,\n    placement: \"bottom-left\",\n    className: \"group\",\n    dropdownClassName: \"border-t-2 border-primary\",\n    emptyMessage: `No ${type} available`\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 5\n  }, this);\n};\n_s(NavigationDropdown, \"dSYAOEQJpmUEaAadZBeAZJtT6G0=\", false, function () {\n  return [useLanguage];\n});\n_c = NavigationDropdown;\nexport default NavigationDropdown;\nvar _c;\n$RefreshReg$(_c, \"NavigationDropdown\");", "map": {"version": 3, "names": ["React", "useState", "ChevronDown", "MapPin", "GraduationCap", "Briefcase", "Dropdown", "useLanguage", "jsxDEV", "_jsxDEV", "NavigationDropdown", "type", "label", "className", "_s", "items", "setItems", "loading", "setLoading", "translations", "fetchData", "length", "endpoint", "dataProcessor", "countries", "id", "navigation", "allCountries", "href", "icon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "count", "reduce", "sum", "c", "slice", "map", "country", "slug", "name", "encodeURIComponent", "viewAll", "levels", "allScholarships", "l", "level", "openCount", "types", "allOpportunities", "t", "opType", "char<PERSON>t", "toUpperCase", "activeCount", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "Error", "result", "json", "data", "error", "console", "disabled", "getIcon", "trigger", "children", "onOpen", "showOnHover", "closeOnClick", "placement", "dropdownClassName", "emptyMessage", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx"], "sourcesContent": ["/**\n * Navigation Dropdown Component\n * \n * Specialized dropdown for navigation menus with data fetching,\n * proper routing, and professional styling.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown, MapPin, GraduationCap, Briefcase } from 'lucide-react';\nimport Dropdown, { DropdownItem } from '../common/Dropdown';\nimport { useLanguage } from '../../context/LanguageContext';\n\ninterface NavigationDropdownProps {\n  type: 'countries' | 'scholarships' | 'opportunities';\n  label: string;\n  className?: string;\n}\n\ninterface CountryData {\n  name: string;\n  count: number;\n  slug: string;\n}\n\ninterface LevelData {\n  name: string;\n  count: number;\n  openCount: number;\n  slug: string;\n}\n\ninterface OpportunityTypeData {\n  name: string;\n  count: number;\n  activeCount: number;\n  slug: string;\n}\n\nconst NavigationDropdown: React.FC<NavigationDropdownProps> = ({\n  type,\n  label,\n  className = ''\n}) => {\n  const [items, setItems] = useState<DropdownItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const { translations } = useLanguage();\n\n  // Fetch data when dropdown opens\n  const fetchData = async () => {\n    if (items.length > 0) return; // Don't refetch if we already have data\n    \n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor: (data: any[]) => DropdownItem[] = () => [];\n\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = (countries: CountryData[]) => [\n            {\n              id: 'all-countries',\n              label: translations.navigation.allCountries || 'All Countries',\n              href: '/countries',\n              icon: <MapPin size={16} />,\n              count: countries.reduce((sum, c) => sum + c.count, 0)\n            },\n            ...countries.slice(0, 8).map(country => ({\n              id: country.slug,\n              label: country.name,\n              href: `/countries/${encodeURIComponent(country.name)}`,\n              count: country.count\n            })),\n            ...(countries.length > 8 ? [{\n              id: 'view-all-countries',\n              label: translations.navigation.viewAll || 'View All',\n              href: '/countries'\n            }] : [])\n          ];\n          break;\n\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = (levels: LevelData[]) => [\n            {\n              id: 'all-scholarships',\n              label: translations.navigation.allScholarships || 'All Scholarships',\n              href: '/scholarships',\n              icon: <GraduationCap size={16} />,\n              count: levels.reduce((sum, l) => sum + l.count, 0)\n            },\n            ...levels.map(level => ({\n              id: level.slug,\n              label: level.name,\n              href: `/scholarships?level=${encodeURIComponent(level.name)}`,\n              count: level.openCount\n            }))\n          ];\n          break;\n\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = (types: OpportunityTypeData[]) => [\n            {\n              id: 'all-opportunities',\n              label: translations.navigation.allOpportunities || 'All Opportunities',\n              href: '/opportunities',\n              icon: <Briefcase size={16} />,\n              count: types.reduce((sum, t) => sum + t.count, 0)\n            },\n            ...types.map(opType => ({\n              id: opType.slug,\n              label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n              href: `/opportunities?type=${encodeURIComponent(opType.name)}`,\n              count: opType.activeCount\n            }))\n          ];\n          break;\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      \n      const result = await response.json();\n      const data = result.data || result;\n      \n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        disabled: true\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getIcon = () => {\n    switch (type) {\n      case 'countries':\n        return <MapPin size={16} className=\"text-gray-500\" />;\n      case 'scholarships':\n        return <GraduationCap size={16} className=\"text-gray-500\" />;\n      case 'opportunities':\n        return <Briefcase size={16} className=\"text-gray-500\" />;\n      default:\n        return null;\n    }\n  };\n\n  const trigger = (\n    <div className={`\n      flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n      transition-all duration-300 ease-in-out\n      text-gray-700 hover:text-primary hover:bg-primary/5 hover:shadow-sm\n      group-hover:scale-105 transform\n      ${className}\n    `}>\n      {getIcon()}\n      <span className=\"transition-all duration-200\">{label}</span>\n      <ChevronDown\n        size={16}\n        className=\"text-gray-400 transition-all duration-300 ease-out group-hover:rotate-180 group-hover:text-primary\"\n      />\n    </div>\n  );\n\n  return (\n    <Dropdown\n      trigger={trigger}\n      items={items}\n      loading={loading}\n      onOpen={fetchData}\n      showOnHover={true}\n      closeOnClick={true}\n      placement=\"bottom-left\"\n      className=\"group\"\n      dropdownClassName=\"border-t-2 border-primary\"\n      emptyMessage={`No ${type} available`}\n    />\n  );\n};\n\nexport default NavigationDropdown;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAElD,SAASC,WAAW,EAAEC,MAAM,EAAEC,aAAa,EAAEC,SAAS,QAAQ,cAAc;AAC5E,OAAOC,QAAQ,MAAwB,oBAAoB;AAC3D,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA4B5D,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,IAAI;EACJC,KAAK;EACLC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAiB,EAAE,CAAC;EACtD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEkB;EAAa,CAAC,GAAGZ,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAMa,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;;IAE9BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAII,QAAQ,GAAG,EAAE;MACjB,IAAIC,aAA8C,GAAGA,CAAA,KAAM,EAAE;MAE7D,QAAQZ,IAAI;QACV,KAAK,WAAW;UACdW,QAAQ,GAAG,gBAAgB;UAC3BC,aAAa,GAAIC,SAAwB,IAAK,CAC5C;YACEC,EAAE,EAAE,eAAe;YACnBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACC,YAAY,IAAI,eAAe;YAC9DC,IAAI,EAAE,YAAY;YAClBC,IAAI,eAAEpB,OAAA,CAACN,MAAM;cAAC2B,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YAC1BC,KAAK,EAAEX,SAAS,CAACY,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACH,KAAK,EAAE,CAAC;UACtD,CAAC,EACD,GAAGX,SAAS,CAACe,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,OAAO,KAAK;YACvChB,EAAE,EAAEgB,OAAO,CAACC,IAAI;YAChB9B,KAAK,EAAE6B,OAAO,CAACE,IAAI;YACnBf,IAAI,EAAE,cAAcgB,kBAAkB,CAACH,OAAO,CAACE,IAAI,CAAC,EAAE;YACtDR,KAAK,EAAEM,OAAO,CAACN;UACjB,CAAC,CAAC,CAAC,EACH,IAAIX,SAAS,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC;YAC1BI,EAAE,EAAE,oBAAoB;YACxBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACmB,OAAO,IAAI,UAAU;YACpDjB,IAAI,EAAE;UACR,CAAC,CAAC,GAAG,EAAE,CAAC,CACT;UACD;QAEF,KAAK,cAAc;UACjBN,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIuB,MAAmB,IAAK,CACvC;YACErB,EAAE,EAAE,kBAAkB;YACtBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACqB,eAAe,IAAI,kBAAkB;YACpEnB,IAAI,EAAE,eAAe;YACrBC,IAAI,eAAEpB,OAAA,CAACL,aAAa;cAAC0B,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YACjCC,KAAK,EAAEW,MAAM,CAACV,MAAM,CAAC,CAACC,GAAG,EAAEW,CAAC,KAAKX,GAAG,GAAGW,CAAC,CAACb,KAAK,EAAE,CAAC;UACnD,CAAC,EACD,GAAGW,MAAM,CAACN,GAAG,CAACS,KAAK,KAAK;YACtBxB,EAAE,EAAEwB,KAAK,CAACP,IAAI;YACd9B,KAAK,EAAEqC,KAAK,CAACN,IAAI;YACjBf,IAAI,EAAE,uBAAuBgB,kBAAkB,CAACK,KAAK,CAACN,IAAI,CAAC,EAAE;YAC7DR,KAAK,EAAEc,KAAK,CAACC;UACf,CAAC,CAAC,CAAC,CACJ;UACD;QAEF,KAAK,eAAe;UAClB5B,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAI4B,KAA4B,IAAK,CAChD;YACE1B,EAAE,EAAE,mBAAmB;YACvBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAAC0B,gBAAgB,IAAI,mBAAmB;YACtExB,IAAI,EAAE,gBAAgB;YACtBC,IAAI,eAAEpB,OAAA,CAACJ,SAAS;cAACyB,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YAC7BC,KAAK,EAAEgB,KAAK,CAACf,MAAM,CAAC,CAACC,GAAG,EAAEgB,CAAC,KAAKhB,GAAG,GAAGgB,CAAC,CAAClB,KAAK,EAAE,CAAC;UAClD,CAAC,EACD,GAAGgB,KAAK,CAACX,GAAG,CAACc,MAAM,KAAK;YACtB7B,EAAE,EAAE6B,MAAM,CAACZ,IAAI;YACf9B,KAAK,EAAE0C,MAAM,CAACX,IAAI,CAACY,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACX,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC;YACjEX,IAAI,EAAE,uBAAuBgB,kBAAkB,CAACU,MAAM,CAACX,IAAI,CAAC,EAAE;YAC9DR,KAAK,EAAEmB,MAAM,CAACG;UAChB,CAAC,CAAC,CAAC,CACJ;UACD;MACJ;MAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,GAAGxC,QAAQ,EAAE,CAAC;MACtG,IAAI,CAACoC,QAAQ,CAACK,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MAEzD,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpC,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAIF,MAAM;MAElCjD,QAAQ,CAACO,aAAa,CAAC4C,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkBzD,IAAI,QAAQ,EAAEyD,KAAK,CAAC;MACpDpD,QAAQ,CAAC,CAAC;QACRS,EAAE,EAAE,OAAO;QACXb,KAAK,EAAE,qBAAqB;QAC5B0D,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRpD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqD,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQ5D,IAAI;MACV,KAAK,WAAW;QACd,oBAAOF,OAAA,CAACN,MAAM;UAAC2B,IAAI,EAAE,EAAG;UAACjB,SAAS,EAAC;QAAe;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,cAAc;QACjB,oBAAOzB,OAAA,CAACL,aAAa;UAAC0B,IAAI,EAAE,EAAG;UAACjB,SAAS,EAAC;QAAe;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9D,KAAK,eAAe;QAClB,oBAAOzB,OAAA,CAACJ,SAAS;UAACyB,IAAI,EAAE,EAAG;UAACjB,SAAS,EAAC;QAAe;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1D;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMsC,OAAO,gBACX/D,OAAA;IAAKI,SAAS,EAAE;AACpB;AACA;AACA;AACA;AACA,QAAQA,SAAS;AACjB,KAAM;IAAA4D,QAAA,GACCF,OAAO,CAAC,CAAC,eACV9D,OAAA;MAAMI,SAAS,EAAC,6BAA6B;MAAA4D,QAAA,EAAE7D;IAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC5DzB,OAAA,CAACP,WAAW;MACV4B,IAAI,EAAE,EAAG;MACTjB,SAAS,EAAC;IAAoG;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/G,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,oBACEzB,OAAA,CAACH,QAAQ;IACPkE,OAAO,EAAEA,OAAQ;IACjBzD,KAAK,EAAEA,KAAM;IACbE,OAAO,EAAEA,OAAQ;IACjByD,MAAM,EAAEtD,SAAU;IAClBuD,WAAW,EAAE,IAAK;IAClBC,YAAY,EAAE,IAAK;IACnBC,SAAS,EAAC,aAAa;IACvBhE,SAAS,EAAC,OAAO;IACjBiE,iBAAiB,EAAC,2BAA2B;IAC7CC,YAAY,EAAE,MAAMpE,IAAI;EAAa;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtC,CAAC;AAEN,CAAC;AAACpB,EAAA,CAjJIJ,kBAAqD;EAAA,QAOhCH,WAAW;AAAA;AAAAyE,EAAA,GAPhCtE,kBAAqD;AAmJ3D,eAAeA,kBAAkB;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}