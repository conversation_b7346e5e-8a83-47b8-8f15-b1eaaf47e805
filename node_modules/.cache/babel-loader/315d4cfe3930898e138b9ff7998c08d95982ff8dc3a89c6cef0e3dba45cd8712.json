{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx\";\nimport React from 'react';\nimport { calculateDaysRemaining } from '../utils/dateFormatter';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScholarshipCard = ({\n  id,\n  title,\n  thumbnail,\n  deadline,\n  isOpen,\n  onClick,\n  level,\n  country\n}) => {\n  const {\n    formattedText,\n    isOpen: isNotExpired\n  } = calculateDaysRemaining(deadline);\n\n  // Use the calculated isOpen status if available, otherwise use the prop\n  const scholarshipStatus = isNotExpired !== undefined ? isNotExpired : isOpen;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"group bg-white rounded-xl shadow-sm overflow-hidden w-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border border-gray-100 flex flex-col cursor-pointer\",\n    onClick: () => onClick(id),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-full aspect-[16/9] overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: thumbnail ? `http://localhost:5000${thumbnail}` : 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',\n        alt: title,\n        className: \"w-full h-full object-cover transform transition-transform duration-500 group-hover:scale-105\",\n        loading: \"lazy\",\n        onError: e => {\n          const target = e.target;\n          target.src = 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), level && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-3 left-3 z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `px-2 py-1 rounded-full text-xs font-medium bg-white/80 backdrop-blur-sm shadow-sm ${level === 'Licence' ? 'text-blue-700' : level === 'Master' ? 'text-purple-700' : 'text-indigo-700'}`,\n          children: level\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this), country && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 bg-gradient-to-r from-black/70 to-transparent w-full py-2 px-3\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-white text-xs font-medium\",\n          children: country\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 flex-grow flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-base font-bold text-gray-900 line-clamp-2 mb-3 group-hover:text-primary transition-colors duration-200\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-4 w-4 mr-1.5 text-gray-400\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm font-medium ${!scholarshipStatus ? 'text-red-600' : formattedText.includes('jour') ? 'text-amber-600' : 'text-gray-600'}`,\n            children: formattedText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${scholarshipStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n          children: scholarshipStatus ? '✅ Ouvert' : '❌ Fermé'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_c = ScholarshipCard;\nexport default ScholarshipCard;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipCard\");", "map": {"version": 3, "names": ["React", "calculateDaysRemaining", "jsxDEV", "_jsxDEV", "ScholarshipCard", "id", "title", "thumbnail", "deadline", "isOpen", "onClick", "level", "country", "formattedText", "isNotExpired", "scholarshipStatus", "undefined", "className", "children", "src", "alt", "loading", "onError", "e", "target", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "includes", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { calculateDaysRemaining } from '../utils/dateFormatter';\n\ninterface ScholarshipCardProps {\n  id: number;\n  title: string;\n  thumbnail: string;\n  deadline: string;\n  isOpen: boolean;\n  onClick: (id: number) => void;\n  level?: string;\n  fundingSource?: string;\n  country?: string;\n}\n\nconst ScholarshipCard: React.FC<ScholarshipCardProps> = ({\n  id,\n  title,\n  thumbnail,\n  deadline,\n  isOpen,\n  onClick,\n  level,\n  country,\n}) => {\n  const { formattedText, isOpen: isNotExpired } = calculateDaysRemaining(deadline);\n\n  // Use the calculated isOpen status if available, otherwise use the prop\n  const scholarshipStatus = isNotExpired !== undefined ? isNotExpired : isOpen;\n\n  return (\n    <div\n      className=\"group bg-white rounded-xl shadow-sm overflow-hidden w-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border border-gray-100 flex flex-col cursor-pointer\"\n      onClick={() => onClick(id)}\n    >\n      {/* Thumbnail - Fixed aspect ratio for consistency */}\n      <div className=\"relative w-full aspect-[16/9] overflow-hidden\">\n        <img\n          src={thumbnail ? `http://localhost:5000${thumbnail}` : 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60'}\n          alt={title}\n          className=\"w-full h-full object-cover transform transition-transform duration-500 group-hover:scale-105\"\n          loading=\"lazy\"\n          onError={(e) => {\n            const target = e.target as HTMLImageElement;\n            target.src = 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60';\n          }}\n        />\n\n        {/* Level badge if available */}\n        {level && (\n          <div className=\"absolute top-3 left-3 z-10\">\n            <span className={`px-2 py-1 rounded-full text-xs font-medium bg-white/80 backdrop-blur-sm shadow-sm ${\n              level === 'Licence' ? 'text-blue-700' :\n              level === 'Master' ? 'text-purple-700' :\n              'text-indigo-700'\n            }`}>\n              {level}\n            </span>\n          </div>\n        )}\n\n        {/* Country overlay */}\n        {country && (\n          <div className=\"absolute bottom-0 left-0 bg-gradient-to-r from-black/70 to-transparent w-full py-2 px-3\">\n            <span className=\"text-white text-xs font-medium\">{country}</span>\n          </div>\n        )}\n      </div>\n\n      {/* Content area */}\n      <div className=\"p-4 flex-grow flex flex-col\">\n        {/* Title - 2 lines max with ellipsis */}\n        <h3 className=\"text-base font-bold text-gray-900 line-clamp-2 mb-3 group-hover:text-primary transition-colors duration-200\">\n          {title}\n        </h3>\n\n        {/* Deadline and status */}\n        <div className=\"mt-auto flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <svg className=\"h-4 w-4 mr-1.5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n            </svg>\n            <span className={`text-sm font-medium ${\n              !scholarshipStatus ? 'text-red-600' :\n              formattedText.includes('jour') ? 'text-amber-600' : 'text-gray-600'\n            }`}>\n              {formattedText}\n            </span>\n          </div>\n\n          {/* Status badge */}\n          <span\n            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${\n              scholarshipStatus\n                ? 'bg-green-100 text-green-800'\n                : 'bg-red-100 text-red-800'\n            }`}\n          >\n            {scholarshipStatus ? '✅ Ouvert' : '❌ Fermé'}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ScholarshipCard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,sBAAsB,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAchE,MAAMC,eAA+C,GAAGA,CAAC;EACvDC,EAAE;EACFC,KAAK;EACLC,SAAS;EACTC,QAAQ;EACRC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAM;IAAEC,aAAa;IAAEJ,MAAM,EAAEK;EAAa,CAAC,GAAGb,sBAAsB,CAACO,QAAQ,CAAC;;EAEhF;EACA,MAAMO,iBAAiB,GAAGD,YAAY,KAAKE,SAAS,GAAGF,YAAY,GAAGL,MAAM;EAE5E,oBACEN,OAAA;IACEc,SAAS,EAAC,iLAAiL;IAC3LP,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAACL,EAAE,CAAE;IAAAa,QAAA,gBAG3Bf,OAAA;MAAKc,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAC5Df,OAAA;QACEgB,GAAG,EAAEZ,SAAS,GAAG,wBAAwBA,SAAS,EAAE,GAAG,6GAA8G;QACrKa,GAAG,EAAEd,KAAM;QACXW,SAAS,EAAC,8FAA8F;QACxGI,OAAO,EAAC,MAAM;QACdC,OAAO,EAAGC,CAAC,IAAK;UACd,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAA0B;UAC3CA,MAAM,CAACL,GAAG,GAAG,6GAA6G;QAC5H;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGDjB,KAAK,iBACJR,OAAA;QAAKc,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCf,OAAA;UAAMc,SAAS,EAAE,qFACfN,KAAK,KAAK,SAAS,GAAG,eAAe,GACrCA,KAAK,KAAK,QAAQ,GAAG,iBAAiB,GACtC,iBAAiB,EAChB;UAAAO,QAAA,EACAP;QAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGAhB,OAAO,iBACNT,OAAA;QAAKc,SAAS,EAAC,yFAAyF;QAAAC,QAAA,eACtGf,OAAA;UAAMc,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAEN;QAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNzB,OAAA;MAAKc,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1Cf,OAAA;QAAIc,SAAS,EAAC,6GAA6G;QAAAC,QAAA,EACxHZ;MAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGLzB,OAAA;QAAKc,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDf,OAAA;UAAKc,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCf,OAAA;YAAKc,SAAS,EAAC,8BAA8B;YAACY,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAb,QAAA,eACjGf,OAAA;cAAM6B,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAwF;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7J,CAAC,eACNzB,OAAA;YAAMc,SAAS,EAAE,uBACf,CAACF,iBAAiB,GAAG,cAAc,GACnCF,aAAa,CAACuB,QAAQ,CAAC,MAAM,CAAC,GAAG,gBAAgB,GAAG,eAAe,EAClE;YAAAlB,QAAA,EACAL;UAAa;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNzB,OAAA;UACEc,SAAS,EAAE,yEACTF,iBAAiB,GACb,6BAA6B,GAC7B,yBAAyB,EAC5B;UAAAG,QAAA,EAEFH,iBAAiB,GAAG,UAAU,GAAG;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACS,EAAA,GAzFIjC,eAA+C;AA2FrD,eAAeA,eAAe;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}