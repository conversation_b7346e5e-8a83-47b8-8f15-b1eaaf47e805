{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx\",\n  _s = $RefreshSig$();\n/**\n * Enhanced Header Component\n * \n * Professional navigation header with dropdown menus,\n * mobile responsiveness, and industry-standard interactions.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Menu, X, Home, Info, Phone } from 'lucide-react';\nimport { useLanguage } from '../../context/LanguageContext';\nimport LanguageSwitcher from '../common/LanguageSwitcher';\nimport NavigationDropdown from '../navigation/NavigationDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EnhancedHeader = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const location = useLocation();\n  const {\n    translations\n  } = useLanguage();\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false);\n  }, [location.pathname]);\n\n  // Prevent body scroll when mobile menu is open\n  useEffect(() => {\n    if (isMobileMenuOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isMobileMenuOpen]);\n  const isActive = path => location.pathname === path;\n  const navigationItems = [{\n    path: '/',\n    label: translations.navigation.home,\n    icon: /*#__PURE__*/_jsxDEV(Home, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this),\n    exact: true\n  }, {\n    path: '/about',\n    label: translations.navigation.about,\n    icon: /*#__PURE__*/_jsxDEV(Info, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 13\n    }, this)\n  }, {\n    path: '/guides',\n    label: translations.navigation.guides,\n    icon: /*#__PURE__*/_jsxDEV(Info, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this)\n  }, {\n    path: '/contact',\n    label: translations.navigation.contact,\n    icon: /*#__PURE__*/_jsxDEV(Phone, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: `\n      fixed w-full top-0 z-50 transition-all duration-300 ease-in-out\n      ${isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100' : 'bg-white shadow-sm'}\n    `,\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-3 group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/assets/images/MaBoursedetudeLogo.jpeg\",\n                alt: translations.brand.name,\n                className: \"h-12 w-auto rounded-lg shadow-md transform transition-transform duration-300 group-hover:scale-105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight\",\n                children: translations.brand.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500 font-medium tracking-wider\",\n                children: translations.brand.tagline\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: `\n                flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n                transition-colors duration-200 ease-in-out\n                ${isActive('/') ? 'text-primary bg-primary/10' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}\n              `,\n            children: [/*#__PURE__*/_jsxDEV(Home, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: translations.navigation.home\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavigationDropdown, {\n            type: \"countries\",\n            label: translations.navigation.countries\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavigationDropdown, {\n            type: \"scholarships\",\n            label: translations.navigation.scholarships\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavigationDropdown, {\n            type: \"opportunities\",\n            label: translations.navigation.opportunities\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), navigationItems.slice(1).map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            className: `\n                  flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive(item.path) ? 'text-primary bg-primary/10' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}\n                `,\n            children: [item.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n              className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary transition-colors duration-200\",\n              \"aria-expanded\": isMobileMenuOpen,\n              \"aria-label\": \"Toggle navigation menu\",\n              children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(X, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200 shadow-lg\",\n        children: [navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.path,\n          className: `\n                  flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive(item.path) ? 'text-primary bg-primary/10 border-l-4 border-primary' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}\n                `,\n          children: [item.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 17\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 15\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider\",\n            children: \"Browse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/countries\",\n            className: \"flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: translations.navigation.countries\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/scholarships\",\n            className: \"flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: translations.navigation.scholarships\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/opportunities\",\n            className: \"flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: translations.navigation.opportunities\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedHeader, \"G0xTpqH9ESwWcM2u40uPD7FC/cg=\", false, function () {\n  return [useLocation, useLanguage];\n});\n_c = EnhancedHeader;\nexport default EnhancedHeader;\nvar _c;\n$RefreshReg$(_c, \"EnhancedHeader\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "<PERSON><PERSON>", "X", "Home", "Info", "Phone", "useLanguage", "LanguageSwitcher", "NavigationDropdown", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "isScrolled", "setIsScrolled", "location", "translations", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "pathname", "document", "body", "style", "overflow", "isActive", "path", "navigationItems", "label", "navigation", "home", "icon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "exact", "about", "guides", "contact", "className", "children", "to", "src", "alt", "brand", "name", "tagline", "type", "countries", "scholarships", "opportunities", "slice", "map", "item", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx"], "sourcesContent": ["/**\n * Enhanced Header Component\n * \n * Professional navigation header with dropdown menus,\n * mobile responsiveness, and industry-standard interactions.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Menu, X, Home, Info, Phone } from 'lucide-react';\nimport { useLanguage } from '../../context/LanguageContext';\nimport LanguageSwitcher from '../common/LanguageSwitcher';\nimport NavigationDropdown from '../navigation/NavigationDropdown';\n\nconst EnhancedHeader: React.FC = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const location = useLocation();\n  const { translations } = useLanguage();\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false);\n  }, [location.pathname]);\n\n  // Prevent body scroll when mobile menu is open\n  useEffect(() => {\n    if (isMobileMenuOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isMobileMenuOpen]);\n\n  const isActive = (path: string) => location.pathname === path;\n\n  const navigationItems = [\n    {\n      path: '/',\n      label: translations.navigation.home,\n      icon: <Home size={16} />,\n      exact: true\n    },\n    {\n      path: '/about',\n      label: translations.navigation.about,\n      icon: <Info size={16} />\n    },\n    {\n      path: '/guides',\n      label: translations.navigation.guides,\n      icon: <Info size={16} />\n    },\n    {\n      path: '/contact',\n      label: translations.navigation.contact,\n      icon: <Phone size={16} />\n    }\n  ];\n\n  return (\n    <header className={`\n      fixed w-full top-0 z-50 transition-all duration-300 ease-in-out\n      ${isScrolled \n        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100' \n        : 'bg-white shadow-sm'\n      }\n    `}>\n      <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n              <div className=\"relative\">\n                <img\n                  src=\"/assets/images/MaBoursedetudeLogo.jpeg\"\n                  alt={translations.brand.name}\n                  className=\"h-12 w-auto rounded-lg shadow-md transform transition-transform duration-300 group-hover:scale-105\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              </div>\n              <div className=\"flex flex-col\">\n                <span className=\"text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight\">\n                  {translations.brand.name}\n                </span>\n                <span className=\"text-xs text-gray-500 font-medium tracking-wider\">\n                  {translations.brand.tagline}\n                </span>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {/* Home Link */}\n            <Link\n              to=\"/\"\n              className={`\n                flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n                transition-colors duration-200 ease-in-out\n                ${isActive('/') \n                  ? 'text-primary bg-primary/10' \n                  : 'text-gray-700 hover:text-primary hover:bg-gray-50'\n                }\n              `}\n            >\n              <Home size={16} />\n              <span>{translations.navigation.home}</span>\n            </Link>\n\n            {/* Dropdown Menus */}\n            <NavigationDropdown\n              type=\"countries\"\n              label={translations.navigation.countries}\n            />\n            \n            <NavigationDropdown\n              type=\"scholarships\"\n              label={translations.navigation.scholarships}\n            />\n            \n            <NavigationDropdown\n              type=\"opportunities\"\n              label={translations.navigation.opportunities}\n            />\n\n            {/* Regular Navigation Items */}\n            {navigationItems.slice(1).map((item) => (\n              <Link\n                key={item.path}\n                to={item.path}\n                className={`\n                  flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive(item.path) \n                    ? 'text-primary bg-primary/10' \n                    : 'text-gray-700 hover:text-primary hover:bg-gray-50'\n                  }\n                `}\n              >\n                {item.icon}\n                <span>{item.label}</span>\n              </Link>\n            ))}\n          </div>\n\n          {/* Right Side - Language Switcher & Mobile Menu */}\n          <div className=\"flex items-center space-x-4\">\n            <LanguageSwitcher />\n            \n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary transition-colors duration-200\"\n                aria-expanded={isMobileMenuOpen}\n                aria-label=\"Toggle navigation menu\"\n              >\n                {isMobileMenuOpen ? (\n                  <X className=\"h-6 w-6\" />\n                ) : (\n                  <Menu className=\"h-6 w-6\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Mobile Navigation Menu */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200 shadow-lg\">\n            {/* Mobile Navigation Items */}\n            {navigationItems.map((item) => (\n              <Link\n                key={item.path}\n                to={item.path}\n                className={`\n                  flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive(item.path) \n                    ? 'text-primary bg-primary/10 border-l-4 border-primary' \n                    : 'text-gray-700 hover:text-primary hover:bg-gray-50'\n                  }\n                `}\n              >\n                {item.icon}\n                <span>{item.label}</span>\n              </Link>\n            ))}\n            \n            {/* Mobile Dropdown Sections */}\n            <div className=\"pt-4 border-t border-gray-200\">\n              <div className=\"px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider\">\n                Browse\n              </div>\n              \n              <Link\n                to=\"/countries\"\n                className=\"flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50\"\n              >\n                <span>{translations.navigation.countries}</span>\n              </Link>\n              \n              <Link\n                to=\"/scholarships\"\n                className=\"flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50\"\n              >\n                <span>{translations.navigation.scholarships}</span>\n              </Link>\n              \n              <Link\n                to=\"/opportunities\"\n                className=\"flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50\"\n              >\n                <span>{translations.navigation.opportunities}</span>\n              </Link>\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default EnhancedHeader;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,cAAc;AACzD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,OAAOC,gBAAgB,MAAM,4BAA4B;AACzD,OAAOC,kBAAkB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMoB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB;EAAa,CAAC,GAAGZ,WAAW,CAAC,CAAC;;EAEtC;EACAR,SAAS,CAAC,MAAM;IACd,MAAMqB,YAAY,GAAGA,CAAA,KAAM;MACzBH,aAAa,CAACI,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArB,SAAS,CAAC,MAAM;IACdgB,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC,EAAE,CAACG,QAAQ,CAACO,QAAQ,CAAC,CAAC;;EAEvB;EACA1B,SAAS,CAAC,MAAM;IACd,IAAIe,gBAAgB,EAAE;MACpBY,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACf,gBAAgB,CAAC,CAAC;EAEtB,MAAMgB,QAAQ,GAAIC,IAAY,IAAKb,QAAQ,CAACO,QAAQ,KAAKM,IAAI;EAE7D,MAAMC,eAAe,GAAG,CACtB;IACED,IAAI,EAAE,GAAG;IACTE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACC,IAAI;IACnCC,IAAI,eAAEzB,OAAA,CAACP,IAAI;MAACiC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACS,KAAK;IACpCP,IAAI,eAAEzB,OAAA,CAACN,IAAI;MAACgC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACzB,CAAC,EACD;IACEV,IAAI,EAAE,SAAS;IACfE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACU,MAAM;IACrCR,IAAI,eAAEzB,OAAA,CAACN,IAAI;MAACgC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACzB,CAAC,EACD;IACEV,IAAI,EAAE,UAAU;IAChBE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACW,OAAO;IACtCT,IAAI,eAAEzB,OAAA,CAACL,KAAK;MAAC+B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC1B,CAAC,CACF;EAED,oBACE9B,OAAA;IAAQmC,SAAS,EAAE;AACvB;AACA,QAAQ9B,UAAU,GACR,iEAAiE,GACjE,oBAAoB;AAC9B,KACM;IAAA+B,QAAA,gBACApC,OAAA;MAAKmC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDpC,OAAA;QAAKmC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDpC,OAAA;UAAKmC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCpC,OAAA,CAACX,IAAI;YAACgD,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBACxDpC,OAAA;cAAKmC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBpC,OAAA;gBACEsC,GAAG,EAAC,wCAAwC;gBAC5CC,GAAG,EAAE/B,YAAY,CAACgC,KAAK,CAACC,IAAK;gBAC7BN,SAAS,EAAC;cAAoG;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC,eACF9B,OAAA;gBAAKmC,SAAS,EAAC;cAAgJ;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC,eACN9B,OAAA;cAAKmC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpC,OAAA;gBAAMmC,SAAS,EAAC,4GAA4G;gBAAAC,QAAA,EACzH5B,YAAY,CAACgC,KAAK,CAACC;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACP9B,OAAA;gBAAMmC,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAC/D5B,YAAY,CAACgC,KAAK,CAACE;cAAO;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN9B,OAAA;UAAKmC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpDpC,OAAA,CAACX,IAAI;YACHgD,EAAE,EAAC,GAAG;YACNF,SAAS,EAAE;AACzB;AACA;AACA,kBAAkBhB,QAAQ,CAAC,GAAG,CAAC,GACX,4BAA4B,GAC5B,mDAAmD;AACvE,eACgB;YAAAiB,QAAA,gBAEFpC,OAAA,CAACP,IAAI;cAACiC,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClB9B,OAAA;cAAAoC,QAAA,EAAO5B,YAAY,CAACe,UAAU,CAACC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eAGP9B,OAAA,CAACF,kBAAkB;YACjB6C,IAAI,EAAC,WAAW;YAChBrB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACqB;UAAU;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAEF9B,OAAA,CAACF,kBAAkB;YACjB6C,IAAI,EAAC,cAAc;YACnBrB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACsB;UAAa;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAEF9B,OAAA,CAACF,kBAAkB;YACjB6C,IAAI,EAAC,eAAe;YACpBrB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACuB;UAAc;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,EAGDT,eAAe,CAAC0B,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAAEC,IAAI,iBACjCjD,OAAA,CAACX,IAAI;YAEHgD,EAAE,EAAEY,IAAI,CAAC7B,IAAK;YACde,SAAS,EAAE;AAC3B;AACA;AACA,oBAAoBhB,QAAQ,CAAC8B,IAAI,CAAC7B,IAAI,CAAC,GACjB,4BAA4B,GAC5B,mDAAmD;AACzE,iBACkB;YAAAgB,QAAA,GAEDa,IAAI,CAACxB,IAAI,eACVzB,OAAA;cAAAoC,QAAA,EAAOa,IAAI,CAAC3B;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAZpBmB,IAAI,CAAC7B,IAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN9B,OAAA;UAAKmC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CpC,OAAA,CAACH,gBAAgB;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGpB9B,OAAA;YAAKmC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBpC,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAM9C,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;cACtDgC,SAAS,EAAC,6MAA6M;cACvN,iBAAehC,gBAAiB;cAChC,cAAW,wBAAwB;cAAAiC,QAAA,EAElCjC,gBAAgB,gBACfH,OAAA,CAACR,CAAC;gBAAC2C,SAAS,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEzB9B,OAAA,CAACT,IAAI;gBAAC4C,SAAS,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC5B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3B,gBAAgB,iBACfH,OAAA;MAAKmC,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBpC,OAAA;QAAKmC,SAAS,EAAC,sEAAsE;QAAAC,QAAA,GAElFf,eAAe,CAAC2B,GAAG,CAAEC,IAAI,iBACxBjD,OAAA,CAACX,IAAI;UAEHgD,EAAE,EAAEY,IAAI,CAAC7B,IAAK;UACde,SAAS,EAAE;AAC3B;AACA;AACA,oBAAoBhB,QAAQ,CAAC8B,IAAI,CAAC7B,IAAI,CAAC,GACjB,sDAAsD,GACtD,mDAAmD;AACzE,iBACkB;UAAAgB,QAAA,GAEDa,IAAI,CAACxB,IAAI,eACVzB,OAAA;YAAAoC,QAAA,EAAOa,IAAI,CAAC3B;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAZpBmB,IAAI,CAAC7B,IAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaV,CACP,CAAC,eAGF9B,OAAA;UAAKmC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CpC,OAAA;YAAKmC,SAAS,EAAC,wEAAwE;YAAAC,QAAA,EAAC;UAExF;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAEN9B,OAAA,CAACX,IAAI;YACHgD,EAAE,EAAC,YAAY;YACfF,SAAS,EAAC,0HAA0H;YAAAC,QAAA,eAEpIpC,OAAA;cAAAoC,QAAA,EAAO5B,YAAY,CAACe,UAAU,CAACqB;YAAS;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAEP9B,OAAA,CAACX,IAAI;YACHgD,EAAE,EAAC,eAAe;YAClBF,SAAS,EAAC,0HAA0H;YAAAC,QAAA,eAEpIpC,OAAA;cAAAoC,QAAA,EAAO5B,YAAY,CAACe,UAAU,CAACsB;YAAY;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAEP9B,OAAA,CAACX,IAAI;YACHgD,EAAE,EAAC,gBAAgB;YACnBF,SAAS,EAAC,0HAA0H;YAAAC,QAAA,eAEpIpC,OAAA;cAAAoC,QAAA,EAAO5B,YAAY,CAACe,UAAU,CAACuB;YAAa;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAC5B,EAAA,CAhOID,cAAwB;EAAA,QAGXX,WAAW,EACHM,WAAW;AAAA;AAAAuD,EAAA,GAJhClD,cAAwB;AAkO9B,eAAeA,cAAc;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}