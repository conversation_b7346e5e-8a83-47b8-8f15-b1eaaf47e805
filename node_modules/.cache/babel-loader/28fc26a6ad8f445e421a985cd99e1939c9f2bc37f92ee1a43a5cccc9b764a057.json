{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { calculateDaysRemaining } from '../utils/dateFormatter';\nimport { constructImageUrl, getImagePlaceholder, preloadImage, ImageLoadState } from '../utils/imageUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScholarshipCard = ({\n  id,\n  title,\n  thumbnail,\n  deadline,\n  isOpen,\n  onClick,\n  level,\n  country\n}) => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(getImagePlaceholder());\n  const [imageState, setImageState] = useState(ImageLoadState.LOADING);\n  const {\n    formattedText,\n    isOpen: isNotExpired\n  } = calculateDaysRemaining(deadline);\n\n  // Use the calculated isOpen status if available, otherwise use the prop\n  const scholarshipStatus = isNotExpired !== undefined ? isNotExpired : isOpen;\n\n  // Load optimized image\n  useEffect(() => {\n    const loadImage = async () => {\n      setImageState(ImageLoadState.LOADING);\n      try {\n        // Try card-sized thumbnail first\n        const cardUrl = constructImageUrl(thumbnail, 'card');\n        const cardState = await preloadImage(cardUrl);\n        if (cardState === ImageLoadState.LOADED) {\n          setImageUrl(cardUrl);\n          setImageState(ImageLoadState.LOADED);\n          return;\n        }\n\n        // Fallback to original image\n        const originalUrl = constructImageUrl(thumbnail, 'original');\n        const originalState = await preloadImage(originalUrl);\n        if (originalState === ImageLoadState.LOADED) {\n          setImageUrl(originalUrl);\n          setImageState(ImageLoadState.LOADED);\n          return;\n        }\n\n        // Final fallback\n        setImageUrl(constructImageUrl(null));\n        setImageState(ImageLoadState.FALLBACK);\n      } catch (error) {\n        console.error('Error loading scholarship image:', error);\n        setImageUrl(constructImageUrl(null));\n        setImageState(ImageLoadState.ERROR);\n      }\n    };\n    loadImage();\n  }, [thumbnail]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"group bg-white rounded-xl shadow-sm overflow-hidden w-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border border-gray-100 flex flex-col cursor-pointer\",\n    onClick: () => onClick(id),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-full aspect-[16/9] overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: thumbnail ? `http://localhost:5000${thumbnail}` : 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60',\n        alt: title,\n        className: \"w-full h-full object-cover transform transition-transform duration-500 group-hover:scale-105\",\n        loading: \"lazy\",\n        onError: e => {\n          const target = e.target;\n          target.src = 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), level && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-3 left-3 z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `px-2 py-1 rounded-full text-xs font-medium bg-white/80 backdrop-blur-sm shadow-sm ${level === 'Licence' ? 'text-blue-700' : level === 'Master' ? 'text-purple-700' : 'text-indigo-700'}`,\n          children: level\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this), country && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 bg-gradient-to-r from-black/70 to-transparent w-full py-2 px-3\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-white text-xs font-medium\",\n          children: country\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 flex-grow flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-base font-bold text-gray-900 line-clamp-2 mb-3 group-hover:text-primary transition-colors duration-200\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-4 w-4 mr-1.5 text-gray-400\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm font-medium ${!scholarshipStatus ? 'text-red-600' : formattedText.includes('jour') ? 'text-amber-600' : 'text-gray-600'}`,\n            children: formattedText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${scholarshipStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n          children: scholarshipStatus ? '✅ Ouvert' : '❌ Fermé'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_s(ScholarshipCard, \"PC9Rmn0OFgPp7hx08A1/E9shZ5Q=\");\n_c = ScholarshipCard;\nexport default ScholarshipCard;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "calculateDaysRemaining", "constructImageUrl", "getImagePlaceholder", "preloadImage", "ImageLoadState", "jsxDEV", "_jsxDEV", "ScholarshipCard", "id", "title", "thumbnail", "deadline", "isOpen", "onClick", "level", "country", "_s", "imageUrl", "setImageUrl", "imageState", "setImageState", "LOADING", "formattedText", "isNotExpired", "scholarshipStatus", "undefined", "loadImage", "cardUrl", "cardState", "LOADED", "originalUrl", "originalState", "FALLBACK", "error", "console", "ERROR", "className", "children", "src", "alt", "loading", "onError", "e", "target", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "includes", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { calculateDaysRemaining } from '../utils/dateFormatter';\nimport { constructImageUrl, handleImageError, getImagePlaceholder, preloadImage, ImageLoadState } from '../utils/imageUtils';\n\ninterface ScholarshipCardProps {\n  id: number;\n  title: string;\n  thumbnail: string;\n  deadline: string;\n  isOpen: boolean;\n  onClick: (id: number) => void;\n  level?: string;\n  fundingSource?: string;\n  country?: string;\n}\n\nconst ScholarshipCard: React.FC<ScholarshipCardProps> = ({\n  id,\n  title,\n  thumbnail,\n  deadline,\n  isOpen,\n  onClick,\n  level,\n  country,\n}) => {\n  const [imageUrl, setImageUrl] = useState<string>(getImagePlaceholder());\n  const [imageState, setImageState] = useState<ImageLoadState>(ImageLoadState.LOADING);\n\n  const { formattedText, isOpen: isNotExpired } = calculateDaysRemaining(deadline);\n\n  // Use the calculated isOpen status if available, otherwise use the prop\n  const scholarshipStatus = isNotExpired !== undefined ? isNotExpired : isOpen;\n\n  // Load optimized image\n  useEffect(() => {\n    const loadImage = async () => {\n      setImageState(ImageLoadState.LOADING);\n\n      try {\n        // Try card-sized thumbnail first\n        const cardUrl = constructImageUrl(thumbnail, 'card');\n        const cardState = await preloadImage(cardUrl);\n\n        if (cardState === ImageLoadState.LOADED) {\n          setImageUrl(cardUrl);\n          setImageState(ImageLoadState.LOADED);\n          return;\n        }\n\n        // Fallback to original image\n        const originalUrl = constructImageUrl(thumbnail, 'original');\n        const originalState = await preloadImage(originalUrl);\n\n        if (originalState === ImageLoadState.LOADED) {\n          setImageUrl(originalUrl);\n          setImageState(ImageLoadState.LOADED);\n          return;\n        }\n\n        // Final fallback\n        setImageUrl(constructImageUrl(null));\n        setImageState(ImageLoadState.FALLBACK);\n      } catch (error) {\n        console.error('Error loading scholarship image:', error);\n        setImageUrl(constructImageUrl(null));\n        setImageState(ImageLoadState.ERROR);\n      }\n    };\n\n    loadImage();\n  }, [thumbnail]);\n\n  return (\n    <div\n      className=\"group bg-white rounded-xl shadow-sm overflow-hidden w-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border border-gray-100 flex flex-col cursor-pointer\"\n      onClick={() => onClick(id)}\n    >\n      {/* Thumbnail - Fixed aspect ratio for consistency */}\n      <div className=\"relative w-full aspect-[16/9] overflow-hidden\">\n        <img\n          src={thumbnail ? `http://localhost:5000${thumbnail}` : 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60'}\n          alt={title}\n          className=\"w-full h-full object-cover transform transition-transform duration-500 group-hover:scale-105\"\n          loading=\"lazy\"\n          onError={(e) => {\n            const target = e.target as HTMLImageElement;\n            target.src = 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60';\n          }}\n        />\n\n        {/* Level badge if available */}\n        {level && (\n          <div className=\"absolute top-3 left-3 z-10\">\n            <span className={`px-2 py-1 rounded-full text-xs font-medium bg-white/80 backdrop-blur-sm shadow-sm ${\n              level === 'Licence' ? 'text-blue-700' :\n              level === 'Master' ? 'text-purple-700' :\n              'text-indigo-700'\n            }`}>\n              {level}\n            </span>\n          </div>\n        )}\n\n        {/* Country overlay */}\n        {country && (\n          <div className=\"absolute bottom-0 left-0 bg-gradient-to-r from-black/70 to-transparent w-full py-2 px-3\">\n            <span className=\"text-white text-xs font-medium\">{country}</span>\n          </div>\n        )}\n      </div>\n\n      {/* Content area */}\n      <div className=\"p-4 flex-grow flex flex-col\">\n        {/* Title - 2 lines max with ellipsis */}\n        <h3 className=\"text-base font-bold text-gray-900 line-clamp-2 mb-3 group-hover:text-primary transition-colors duration-200\">\n          {title}\n        </h3>\n\n        {/* Deadline and status */}\n        <div className=\"mt-auto flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <svg className=\"h-4 w-4 mr-1.5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n            </svg>\n            <span className={`text-sm font-medium ${\n              !scholarshipStatus ? 'text-red-600' :\n              formattedText.includes('jour') ? 'text-amber-600' : 'text-gray-600'\n            }`}>\n              {formattedText}\n            </span>\n          </div>\n\n          {/* Status badge */}\n          <span\n            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${\n              scholarshipStatus\n                ? 'bg-green-100 text-green-800'\n                : 'bg-red-100 text-red-800'\n            }`}\n          >\n            {scholarshipStatus ? '✅ Ouvert' : '❌ Fermé'}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ScholarshipCard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,sBAAsB,QAAQ,wBAAwB;AAC/D,SAASC,iBAAiB,EAAoBC,mBAAmB,EAAEC,YAAY,EAAEC,cAAc,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc7H,MAAMC,eAA+C,GAAGA,CAAC;EACvDC,EAAE;EACFC,KAAK;EACLC,SAAS;EACTC,QAAQ;EACRC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAASI,mBAAmB,CAAC,CAAC,CAAC;EACvE,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAiBM,cAAc,CAACiB,OAAO,CAAC;EAEpF,MAAM;IAAEC,aAAa;IAAEV,MAAM,EAAEW;EAAa,CAAC,GAAGvB,sBAAsB,CAACW,QAAQ,CAAC;;EAEhF;EACA,MAAMa,iBAAiB,GAAGD,YAAY,KAAKE,SAAS,GAAGF,YAAY,GAAGX,MAAM;;EAE5E;EACAb,SAAS,CAAC,MAAM;IACd,MAAM2B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BN,aAAa,CAAChB,cAAc,CAACiB,OAAO,CAAC;MAErC,IAAI;QACF;QACA,MAAMM,OAAO,GAAG1B,iBAAiB,CAACS,SAAS,EAAE,MAAM,CAAC;QACpD,MAAMkB,SAAS,GAAG,MAAMzB,YAAY,CAACwB,OAAO,CAAC;QAE7C,IAAIC,SAAS,KAAKxB,cAAc,CAACyB,MAAM,EAAE;UACvCX,WAAW,CAACS,OAAO,CAAC;UACpBP,aAAa,CAAChB,cAAc,CAACyB,MAAM,CAAC;UACpC;QACF;;QAEA;QACA,MAAMC,WAAW,GAAG7B,iBAAiB,CAACS,SAAS,EAAE,UAAU,CAAC;QAC5D,MAAMqB,aAAa,GAAG,MAAM5B,YAAY,CAAC2B,WAAW,CAAC;QAErD,IAAIC,aAAa,KAAK3B,cAAc,CAACyB,MAAM,EAAE;UAC3CX,WAAW,CAACY,WAAW,CAAC;UACxBV,aAAa,CAAChB,cAAc,CAACyB,MAAM,CAAC;UACpC;QACF;;QAEA;QACAX,WAAW,CAACjB,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACpCmB,aAAa,CAAChB,cAAc,CAAC4B,QAAQ,CAAC;MACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDf,WAAW,CAACjB,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACpCmB,aAAa,CAAChB,cAAc,CAAC+B,KAAK,CAAC;MACrC;IACF,CAAC;IAEDT,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAChB,SAAS,CAAC,CAAC;EAEf,oBACEJ,OAAA;IACE8B,SAAS,EAAC,iLAAiL;IAC3LvB,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAACL,EAAE,CAAE;IAAA6B,QAAA,gBAG3B/B,OAAA;MAAK8B,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAC5D/B,OAAA;QACEgC,GAAG,EAAE5B,SAAS,GAAG,wBAAwBA,SAAS,EAAE,GAAG,6GAA8G;QACrK6B,GAAG,EAAE9B,KAAM;QACX2B,SAAS,EAAC,8FAA8F;QACxGI,OAAO,EAAC,MAAM;QACdC,OAAO,EAAGC,CAAC,IAAK;UACd,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAA0B;UAC3CA,MAAM,CAACL,GAAG,GAAG,6GAA6G;QAC5H;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGDjC,KAAK,iBACJR,OAAA;QAAK8B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzC/B,OAAA;UAAM8B,SAAS,EAAE,qFACftB,KAAK,KAAK,SAAS,GAAG,eAAe,GACrCA,KAAK,KAAK,QAAQ,GAAG,iBAAiB,GACtC,iBAAiB,EAChB;UAAAuB,QAAA,EACAvB;QAAK;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGAhC,OAAO,iBACNT,OAAA;QAAK8B,SAAS,EAAC,yFAAyF;QAAAC,QAAA,eACtG/B,OAAA;UAAM8B,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAEtB;QAAO;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNzC,OAAA;MAAK8B,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1C/B,OAAA;QAAI8B,SAAS,EAAC,6GAA6G;QAAAC,QAAA,EACxH5B;MAAK;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGLzC,OAAA;QAAK8B,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxD/B,OAAA;UAAK8B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/B,OAAA;YAAK8B,SAAS,EAAC,8BAA8B;YAACY,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAb,QAAA,eACjG/B,OAAA;cAAM6C,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAwF;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7J,CAAC,eACNzC,OAAA;YAAM8B,SAAS,EAAE,uBACf,CAACZ,iBAAiB,GAAG,cAAc,GACnCF,aAAa,CAACiC,QAAQ,CAAC,MAAM,CAAC,GAAG,gBAAgB,GAAG,eAAe,EAClE;YAAAlB,QAAA,EACAf;UAAa;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNzC,OAAA;UACE8B,SAAS,EAAE,yEACTZ,iBAAiB,GACb,6BAA6B,GAC7B,yBAAyB,EAC5B;UAAAa,QAAA,EAEFb,iBAAiB,GAAG,UAAU,GAAG;QAAS;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAnIIT,eAA+C;AAAAiD,EAAA,GAA/CjD,eAA+C;AAqIrD,eAAeA,eAAe;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}