{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport validateEnv from './utils/envValidator';\n\n// Fix for mobile Safari viewport height issues\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction setViewportHeight() {\n  const vh = window.innerHeight * 0.01;\n  document.documentElement.style.setProperty('--vh', `${vh}px`);\n}\n\n// Set initial viewport height\nsetViewportHeight();\n\n// Update on resize and orientation change\nwindow.addEventListener('resize', setViewportHeight);\nwindow.addEventListener('orientationchange', () => {\n  setTimeout(setViewportHeight, 100);\n});\n\n// Validate environment variables\nvalidateEnv();\n\n// Log application startup information\nconsole.info(`Starting MaBourse application in ${process.env.NODE_ENV} mode`);\nconsole.info(`API URL: ${process.env.REACT_APP_API_URL}`);\nconsole.info(`Using real API: ${process.env.REACT_APP_USE_REAL_API}`);\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 35,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "validateEnv", "jsxDEV", "_jsxDEV", "setViewportHeight", "vh", "window", "innerHeight", "document", "documentElement", "style", "setProperty", "addEventListener", "setTimeout", "console", "info", "process", "env", "NODE_ENV", "REACT_APP_API_URL", "REACT_APP_USE_REAL_API", "root", "createRoot", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport validateEnv from './utils/envValidator';\n\n// Fix for mobile Safari viewport height issues\nfunction setViewportHeight() {\n  const vh = window.innerHeight * 0.01;\n  document.documentElement.style.setProperty('--vh', `${vh}px`);\n}\n\n// Set initial viewport height\nsetViewportHeight();\n\n// Update on resize and orientation change\nwindow.addEventListener('resize', setViewportHeight);\nwindow.addEventListener('orientationchange', () => {\n  setTimeout(setViewportHeight, 100);\n});\n\n// Validate environment variables\nvalidateEnv();\n\n// Log application startup information\nconsole.info(`Starting MaBourse application in ${process.env.NODE_ENV} mode`);\nconsole.info(`API URL: ${process.env.REACT_APP_API_URL}`);\nconsole.info(`Using real API: ${process.env.REACT_APP_USE_REAL_API}`);\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,WAAW,MAAM,sBAAsB;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,MAAMC,EAAE,GAAGC,MAAM,CAACC,WAAW,GAAG,IAAI;EACpCC,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,MAAM,EAAE,GAAGN,EAAE,IAAI,CAAC;AAC/D;;AAEA;AACAD,iBAAiB,CAAC,CAAC;;AAEnB;AACAE,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAER,iBAAiB,CAAC;AACpDE,MAAM,CAACM,gBAAgB,CAAC,mBAAmB,EAAE,MAAM;EACjDC,UAAU,CAACT,iBAAiB,EAAE,GAAG,CAAC;AACpC,CAAC,CAAC;;AAEF;AACAH,WAAW,CAAC,CAAC;;AAEb;AACAa,OAAO,CAACC,IAAI,CAAC,oCAAoCC,OAAO,CAACC,GAAG,CAACC,QAAQ,OAAO,CAAC;AAC7EJ,OAAO,CAACC,IAAI,CAAC,YAAYC,OAAO,CAACC,GAAG,CAACE,iBAAiB,EAAE,CAAC;AACzDL,OAAO,CAACC,IAAI,CAAC,mBAAmBC,OAAO,CAACC,GAAG,CAACG,sBAAsB,EAAE,CAAC;AAErE,MAAMC,IAAI,GAAGtB,QAAQ,CAACuB,UAAU,CAC9Bd,QAAQ,CAACe,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDF,IAAI,CAACG,MAAM,cACTrB,OAAA,CAACL,KAAK,CAAC2B,UAAU;EAAAC,QAAA,eACfvB,OAAA,CAACH,GAAG;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}